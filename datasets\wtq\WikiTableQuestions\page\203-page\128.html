<!-- http%3A//en.wikipedia.org/wiki%3Faction%3Drender%26curid%3D2405344%26oldid%3D601687338 2014-06-12-19-50-16 -->
<table class="metadata plainlinks ambox ambox-content ambox-multiple_issues compact-ambox" role="presentation">
<tr>
<td class="mbox-image">
<div style="width:52px;"><img alt="" src="//upload.wikimedia.org/wikipedia/en/f/f4/Ambox_content.png" width="40" height="40" data-file-width="40" data-file-height="40" /></div>
</td>
<td class="mbox-text">
<table class="collapsible" style="width:95%; background:transparent;">
<tr>
<th style="text-align:left; padding:0.2em 2px 0.2em 0;">This article has multiple issues. <span style="font-weight: normal;">Please help <b><a class="external text" href="//en.wikipedia.org/w/index.php?title=Portable_character_set&amp;action=edit">improve it</a></b> or discuss these issues on the <b><a href="//en.wikipedia.org/wiki/Talk:Portable_character_set" title="Talk:Portable character set">talk page</a></b>.</span></th>
</tr>
<tr>
<td>
<table class="metadata plainlinks ambox ambox-content ambox-Unreferenced" role="presentation">
<tr>
<td class="mbox-image">
<div style="width:52px;"><a href="//en.wikipedia.org/wiki/File:Question_book-new.svg" class="image"><img alt="Question book-new.svg" src="//upload.wikimedia.org/wikipedia/en/thumb/9/99/Question_book-new.svg/50px-Question_book-new.svg.png" width="50" height="39" srcset="//upload.wikimedia.org/wikipedia/en/thumb/9/99/Question_book-new.svg/75px-Question_book-new.svg.png 1.5x, //upload.wikimedia.org/wikipedia/en/thumb/9/99/Question_book-new.svg/100px-Question_book-new.svg.png 2x" data-file-width="262" data-file-height="204" /></a></div>
</td>
<td class="mbox-text"><span class="mbox-text-span">This article <b>does not <a href="//en.wikipedia.org/wiki/Wikipedia:Citing_sources" title="Wikipedia:Citing sources">cite</a> any <a href="//en.wikipedia.org/wiki/Wikipedia:Verifiability" title="Wikipedia:Verifiability">references or sources</a></b>. <span class="hide-when-compact">Please help <a class="external text" href="//en.wikipedia.org/w/index.php?title=Portable_character_set&amp;action=edit">improve this article</a> by <a href="//en.wikipedia.org/wiki/Help:Introduction_to_referencing/1" title="Help:Introduction to referencing/1">adding citations to reliable sources</a>. Unsourced material may be challenged and <a href="//en.wikipedia.org/wiki/Wikipedia:Verifiability#Burden_of_evidence" title="Wikipedia:Verifiability">removed</a>.</span> <small><i>(December 2009)</i></small></span></td>
</tr>
</table>
<table class="metadata plainlinks ambox ambox-style ambox-Orphan" role="presentation">
<tr>
<td class="mbox-image">
<div style="width:52px;"><a href="//en.wikipedia.org/wiki/File:Wiki_letter_w.svg" class="image"><img alt="Wiki letter w.svg" src="//upload.wikimedia.org/wikipedia/en/thumb/6/6c/Wiki_letter_w.svg/40px-Wiki_letter_w.svg.png" width="40" height="40" srcset="//upload.wikimedia.org/wikipedia/en/thumb/6/6c/Wiki_letter_w.svg/60px-Wiki_letter_w.svg.png 1.5x, //upload.wikimedia.org/wikipedia/en/thumb/6/6c/Wiki_letter_w.svg/80px-Wiki_letter_w.svg.png 2x" data-file-width="44" data-file-height="44" /></a></div>
</td>
<td class="mbox-text"><span class="mbox-text-span">This article <b>is an <a href="//en.wikipedia.org/wiki/Wikipedia:Orphan" title="Wikipedia:Orphan">orphan</a>, as no other articles <a class="external text" href="//en.wikipedia.org/w/index.php?title=Special:Whatlinkshere&amp;target=Portable_character_set&amp;namespace=0">link to it</a></b>. Please <a href="//en.wikipedia.org/wiki/Help:Link" title="Help:Link">introduce links</a> to this page from <span class="plainlinks"><a class="external text" href="//en.wikipedia.org/w/index.php?title=Special%3ASearch&amp;redirs=1&amp;search=Portable+character+set&amp;fulltext=Search&amp;ns0=1&amp;title=Special%3ASearch&amp;advanced=1&amp;fulltext=Portable+character+set">related articles</a></span>; try the <a rel="nofollow" class="external text" href="//edwardbetts.com/find_link?q=Portable_character_set">Find links tool</a> for suggestions. <small><i>(December 2009)</i></small></span></td>
</tr>
</table>
</td>
</tr>
</table>
</td>
</tr>
</table>
<p><b>Portable Character Set</b> is a set of 103 characters which, according to the <a href="//en.wikipedia.org/wiki/POSIX" title="POSIX">POSIX</a> standard, must be present in any character set. It is a subset of <a href="//en.wikipedia.org/wiki/ASCII" title="ASCII">ASCII</a>, lacking some <a href="//en.wikipedia.org/wiki/Control_character" title="Control character">control characters</a>.<a rel="nofollow" class="external autonumber" href="http://www.opengroup.org/onlinepubs/000095399/basedefs/xbd_chap06.html">[1]</a></p>
<table class="wikitable">
<tr style="background: #F0FFF0">
<th>name</th>
<th>glyph</th>
<th><a href="//en.wikipedia.org/wiki/C_(programming_language)" title="C (programming language)">C</a> string</th>
<th><a href="//en.wikipedia.org/wiki/Unicode" title="Unicode">Unicode</a></th>
<th>Unicode name</th>
</tr>
<tr>
<td>NUL</td>
<td>&#160;</td>
<td>\0</td>
<td>U+0000</td>
<td>NULL (NUL)</td>
</tr>
<tr>
<td>alert</td>
<td>&#160;</td>
<td>\a</td>
<td>U+0007</td>
<td>BELL (BEL)</td>
</tr>
<tr>
<td>backspace</td>
<td>&#160;</td>
<td>\b</td>
<td>U+0008</td>
<td>BACKSPACE (BS)</td>
</tr>
<tr>
<td>tab</td>
<td>&#160;</td>
<td>\t</td>
<td>U+0009</td>
<td>CHARACTER TABULATION (HT)</td>
</tr>
<tr>
<td>carriage-return</td>
<td>&#160;</td>
<td>\r</td>
<td>U+000D</td>
<td>CARRIAGE RETURN (CR)</td>
</tr>
<tr>
<td>newline</td>
<td>&#160;</td>
<td>\n</td>
<td>U+000A</td>
<td>LINE FEED (LF)</td>
</tr>
<tr>
<td>vertical-tab</td>
<td>&#160;</td>
<td>\v</td>
<td>U+000B</td>
<td>LINE TABULATION (VT)</td>
</tr>
<tr>
<td>form-feed</td>
<td>&#160;</td>
<td>\f</td>
<td>U+000C</td>
<td>FORM FEED (FF)</td>
</tr>
<tr>
<td>space</td>
<td>&#160;</td>
<td>&#160;</td>
<td>U+0020</td>
<td>SPACE</td>
</tr>
<tr>
<td>exclamation-mark</td>
<td>&#160;!</td>
<td>&#160;!</td>
<td>U+0021</td>
<td>EXCLAMATION MARK</td>
</tr>
<tr>
<td>quotation-mark</td>
<td>"</td>
<td>\"</td>
<td>U+0022</td>
<td>QUOTATION MARK</td>
</tr>
<tr>
<td>number-sign</td>
<td>#</td>
<td>#</td>
<td>U+0023</td>
<td>NUMBER SIGN</td>
</tr>
<tr>
<td>dollar-sign</td>
<td>$</td>
<td>$</td>
<td>U+0024</td>
<td>DOLLAR SIGN</td>
</tr>
<tr>
<td>percent-sign</td>
<td>&#160;%</td>
<td>&#160;%</td>
<td>U+0025</td>
<td>PERCENT SIGN</td>
</tr>
<tr>
<td>ampersand</td>
<td>&amp;</td>
<td>&amp;</td>
<td>U+0026</td>
<td>AMPERSAND</td>
</tr>
<tr>
<td>apostrophe</td>
<td>'</td>
<td>\'</td>
<td>U+0027</td>
<td>APOSTROPHE</td>
</tr>
<tr>
<td>left-parenthesis</td>
<td>(</td>
<td>(</td>
<td>U+0028</td>
<td>LEFT PARENTHESIS</td>
</tr>
<tr>
<td>right-parenthesis</td>
<td>)</td>
<td>)</td>
<td>U+0029</td>
<td>RIGHT PARENTHESIS</td>
</tr>
<tr>
<td>asterisk</td>
<td>*</td>
<td>*</td>
<td>U+002A</td>
<td>ASTERISK</td>
</tr>
<tr>
<td>plus-sign</td>
<td>+</td>
<td>+</td>
<td>U+002B</td>
<td>PLUS SIGN</td>
</tr>
<tr>
<td>comma</td>
<td>,</td>
<td>,</td>
<td>U+002C</td>
<td>COMMA</td>
</tr>
<tr>
<td>hyphen</td>
<td>-</td>
<td>-</td>
<td>U+002D</td>
<td>HYPHEN-MINUS</td>
</tr>
<tr>
<td>period</td>
<td>.</td>
<td>.</td>
<td>U+002E</td>
<td>FULL STOP</td>
</tr>
<tr>
<td>slash</td>
<td>/</td>
<td>/</td>
<td>U+002F</td>
<td>SOLIDUS</td>
</tr>
<tr>
<td>zero</td>
<td>0</td>
<td>0</td>
<td>U+0030</td>
<td>DIGIT ZERO</td>
</tr>
<tr>
<td>one</td>
<td>1</td>
<td>1</td>
<td>U+0031</td>
<td>DIGIT ONE</td>
</tr>
<tr>
<td>two</td>
<td>2</td>
<td>2</td>
<td>U+0032</td>
<td>DIGIT TWO</td>
</tr>
<tr>
<td>three</td>
<td>3</td>
<td>3</td>
<td>U+0033</td>
<td>DIGIT THREE</td>
</tr>
<tr>
<td>four</td>
<td>4</td>
<td>4</td>
<td>U+0034</td>
<td>DIGIT FOUR</td>
</tr>
<tr>
<td>five</td>
<td>5</td>
<td>5</td>
<td>U+0035</td>
<td>DIGIT FIVE</td>
</tr>
<tr>
<td>six</td>
<td>6</td>
<td>6</td>
<td>U+0036</td>
<td>DIGIT SIX</td>
</tr>
<tr>
<td>seven</td>
<td>7</td>
<td>7</td>
<td>U+0037</td>
<td>DIGIT SEVEN</td>
</tr>
<tr>
<td>eight</td>
<td>8</td>
<td>8</td>
<td>U+0038</td>
<td>DIGIT EIGHT</td>
</tr>
<tr>
<td>nine</td>
<td>9</td>
<td>9</td>
<td>U+0039</td>
<td>DIGIT NINE</td>
</tr>
<tr>
<td>colon</td>
<td>&#160;:</td>
<td>&#160;:</td>
<td>U+003A</td>
<td>COLON</td>
</tr>
<tr>
<td>semicolon</td>
<td>&#160;;</td>
<td>&#160;;</td>
<td>U+003B</td>
<td>SEMICOLON</td>
</tr>
<tr>
<td>less-than-sign</td>
<td>&lt;</td>
<td>&lt;</td>
<td>U+003C</td>
<td>LESS-THAN SIGN</td>
</tr>
<tr>
<td>equals-sign</td>
<td>=</td>
<td>=</td>
<td>U+003D</td>
<td>EQUALS SIGN</td>
</tr>
<tr>
<td>greater-than-sign</td>
<td>&gt;</td>
<td>&gt;</td>
<td>U+003E</td>
<td>GREATER-THAN SIGN</td>
</tr>
<tr>
<td>question-mark</td>
<td>&#160;?</td>
<td>&#160;?</td>
<td>U+003F</td>
<td>QUESTION MARK</td>
</tr>
<tr>
<td>commercial-at</td>
<td>@</td>
<td>@</td>
<td>U+0040</td>
<td>COMMERCIAL AT</td>
</tr>
<tr>
<td>A</td>
<td>A</td>
<td>A</td>
<td>U+0041</td>
<td>LATIN CAPITAL LETTER A</td>
</tr>
<tr>
<td>B</td>
<td>B</td>
<td>B</td>
<td>U+0042</td>
<td>LATIN CAPITAL LETTER B</td>
</tr>
<tr>
<td>C</td>
<td>C</td>
<td>C</td>
<td>U+0043</td>
<td>LATIN CAPITAL LETTER C</td>
</tr>
<tr>
<td>D</td>
<td>D</td>
<td>D</td>
<td>U+0044</td>
<td>LATIN CAPITAL LETTER D</td>
</tr>
<tr>
<td>E</td>
<td>E</td>
<td>E</td>
<td>U+0045</td>
<td>LATIN CAPITAL LETTER E</td>
</tr>
<tr>
<td>F</td>
<td>F</td>
<td>F</td>
<td>U+0046</td>
<td>LATIN CAPITAL LETTER F</td>
</tr>
<tr>
<td>G</td>
<td>G</td>
<td>G</td>
<td>U+0047</td>
<td>LATIN CAPITAL LETTER G</td>
</tr>
<tr>
<td>H</td>
<td>H</td>
<td>H</td>
<td>U+0048</td>
<td>LATIN CAPITAL LETTER H</td>
</tr>
<tr>
<td>I</td>
<td>I</td>
<td>I</td>
<td>U+0049</td>
<td>LATIN CAPITAL LETTER I</td>
</tr>
<tr>
<td>J</td>
<td>J</td>
<td>J</td>
<td>U+004A</td>
<td>LATIN CAPITAL LETTER J</td>
</tr>
<tr>
<td>K</td>
<td>K</td>
<td>K</td>
<td>U+004B</td>
<td>LATIN CAPITAL LETTER K</td>
</tr>
<tr>
<td>L</td>
<td>L</td>
<td>L</td>
<td>U+004C</td>
<td>LATIN CAPITAL LETTER L</td>
</tr>
<tr>
<td>M</td>
<td>M</td>
<td>M</td>
<td>U+004D</td>
<td>LATIN CAPITAL LETTER M</td>
</tr>
<tr>
<td>N</td>
<td>N</td>
<td>N</td>
<td>U+004E</td>
<td>LATIN CAPITAL LETTER N</td>
</tr>
<tr>
<td>O</td>
<td>O</td>
<td>O</td>
<td>U+004F</td>
<td>LATIN CAPITAL LETTER O</td>
</tr>
<tr>
<td>P</td>
<td>P</td>
<td>P</td>
<td>U+0050</td>
<td>LATIN CAPITAL LETTER P</td>
</tr>
<tr>
<td>Q</td>
<td>Q</td>
<td>Q</td>
<td>U+0051</td>
<td>LATIN CAPITAL LETTER Q</td>
</tr>
<tr>
<td>R</td>
<td>R</td>
<td>R</td>
<td>U+0052</td>
<td>LATIN CAPITAL LETTER R</td>
</tr>
<tr>
<td>S</td>
<td>S</td>
<td>S</td>
<td>U+0053</td>
<td>LATIN CAPITAL LETTER S</td>
</tr>
<tr>
<td>T</td>
<td>T</td>
<td>T</td>
<td>U+0054</td>
<td>LATIN CAPITAL LETTER T</td>
</tr>
<tr>
<td>U</td>
<td>U</td>
<td>U</td>
<td>U+0055</td>
<td>LATIN CAPITAL LETTER U</td>
</tr>
<tr>
<td>V</td>
<td>V</td>
<td>V</td>
<td>U+0056</td>
<td>LATIN CAPITAL LETTER V</td>
</tr>
<tr>
<td>W</td>
<td>W</td>
<td>W</td>
<td>U+0057</td>
<td>LATIN CAPITAL LETTER W</td>
</tr>
<tr>
<td>X</td>
<td>X</td>
<td>X</td>
<td>U+0058</td>
<td>LATIN CAPITAL LETTER X</td>
</tr>
<tr>
<td>Y</td>
<td>Y</td>
<td>Y</td>
<td>U+0059</td>
<td>LATIN CAPITAL LETTER Y</td>
</tr>
<tr>
<td>Z</td>
<td>Z</td>
<td>Z</td>
<td>U+005A</td>
<td>LATIN CAPITAL LETTER Z</td>
</tr>
<tr>
<td>left-square-bracket</td>
<td>[</td>
<td>[</td>
<td>U+005B</td>
<td>LEFT SQUARE BRACKET</td>
</tr>
<tr>
<td>backslash</td>
<td>\</td>
<td>\\</td>
<td>U+005C</td>
<td>REVERSE SOLIDUS</td>
</tr>
<tr>
<td>right-square-bracket</td>
<td>]</td>
<td>]</td>
<td>U+005D</td>
<td>RIGHT SQUARE BRACKET</td>
</tr>
<tr>
<td>circumflex</td>
<td>^</td>
<td>^</td>
<td>U+005E</td>
<td>CIRCUMFLEX ACCENT</td>
</tr>
<tr>
<td>underscore</td>
<td>_</td>
<td>_</td>
<td>U+005F</td>
<td>LOW LINE</td>
</tr>
<tr>
<td>grave-accent</td>
<td>`</td>
<td>`</td>
<td>U+0060</td>
<td>GRAVE ACCENT</td>
</tr>
<tr>
<td>a</td>
<td>a</td>
<td>a</td>
<td>U+0061</td>
<td>LATIN SMALL LETTER A</td>
</tr>
<tr>
<td>b</td>
<td>b</td>
<td>b</td>
<td>U+0062</td>
<td>LATIN SMALL LETTER B</td>
</tr>
<tr>
<td>c</td>
<td>c</td>
<td>c</td>
<td>U+0063</td>
<td>LATIN SMALL LETTER C</td>
</tr>
<tr>
<td>d</td>
<td>d</td>
<td>d</td>
<td>U+0064</td>
<td>LATIN SMALL LETTER D</td>
</tr>
<tr>
<td>e</td>
<td>e</td>
<td>e</td>
<td>U+0065</td>
<td>LATIN SMALL LETTER E</td>
</tr>
<tr>
<td>f</td>
<td>f</td>
<td>f</td>
<td>U+0066</td>
<td>LATIN SMALL LETTER F</td>
</tr>
<tr>
<td>g</td>
<td>g</td>
<td>g</td>
<td>U+0067</td>
<td>LATIN SMALL LETTER G</td>
</tr>
<tr>
<td>h</td>
<td>h</td>
<td>h</td>
<td>U+0068</td>
<td>LATIN SMALL LETTER H</td>
</tr>
<tr>
<td>i</td>
<td>i</td>
<td>i</td>
<td>U+0069</td>
<td>LATIN SMALL LETTER I</td>
</tr>
<tr>
<td>j</td>
<td>j</td>
<td>j</td>
<td>U+006A</td>
<td>LATIN SMALL LETTER J</td>
</tr>
<tr>
<td>k</td>
<td>k</td>
<td>k</td>
<td>U+006B</td>
<td>LATIN SMALL LETTER K</td>
</tr>
<tr>
<td>l</td>
<td>l</td>
<td>l</td>
<td>U+006C</td>
<td>LATIN SMALL LETTER L</td>
</tr>
<tr>
<td>m</td>
<td>m</td>
<td>m</td>
<td>U+006D</td>
<td>LATIN SMALL LETTER M</td>
</tr>
<tr>
<td>n</td>
<td>n</td>
<td>n</td>
<td>U+006E</td>
<td>LATIN SMALL LETTER N</td>
</tr>
<tr>
<td>o</td>
<td>o</td>
<td>o</td>
<td>U+006F</td>
<td>LATIN SMALL LETTER O</td>
</tr>
<tr>
<td>p</td>
<td>p</td>
<td>p</td>
<td>U+0070</td>
<td>LATIN SMALL LETTER P</td>
</tr>
<tr>
<td>q</td>
<td>q</td>
<td>q</td>
<td>U+0071</td>
<td>LATIN SMALL LETTER Q</td>
</tr>
<tr>
<td>r</td>
<td>r</td>
<td>r</td>
<td>U+0072</td>
<td>LATIN SMALL LETTER R</td>
</tr>
<tr>
<td>s</td>
<td>s</td>
<td>s</td>
<td>U+0073</td>
<td>LATIN SMALL LETTER S</td>
</tr>
<tr>
<td>t</td>
<td>t</td>
<td>t</td>
<td>U+0074</td>
<td>LATIN SMALL LETTER T</td>
</tr>
<tr>
<td>u</td>
<td>u</td>
<td>u</td>
<td>U+0075</td>
<td>LATIN SMALL LETTER U</td>
</tr>
<tr>
<td>v</td>
<td>v</td>
<td>v</td>
<td>U+0076</td>
<td>LATIN SMALL LETTER V</td>
</tr>
<tr>
<td>w</td>
<td>w</td>
<td>w</td>
<td>U+0077</td>
<td>LATIN SMALL LETTER W</td>
</tr>
<tr>
<td>x</td>
<td>x</td>
<td>x</td>
<td>U+0078</td>
<td>LATIN SMALL LETTER X</td>
</tr>
<tr>
<td>y</td>
<td>y</td>
<td>y</td>
<td>U+0079</td>
<td>LATIN SMALL LETTER Y</td>
</tr>
<tr>
<td>z</td>
<td>z</td>
<td>z</td>
<td>U+007A</td>
<td>LATIN SMALL LETTER Z</td>
</tr>
<tr>
<td>left-brace</td>
<td>{</td>
<td>{</td>
<td>U+007B</td>
<td>LEFT CURLY BRACKET</td>
</tr>
<tr>
<td>vertical-line</td>
<td>|</td>
<td>|</td>
<td>U+007C</td>
<td>VERTICAL LINE</td>
</tr>
<tr>
<td>right-brace</td>
<td>}</td>
<td>}</td>
<td>U+007D</td>
<td>RIGHT CURLY BRACKET</td>
</tr>
<tr>
<td>tilde</td>
<td>~</td>
<td>~</td>
<td>U+007E</td>
<td>TILDE</td>
</tr>
</table>


<!-- 
NewPP limit report
Parsed by mw1188
CPU time usage: 0.408 seconds
Real time usage: 0.451 seconds
Preprocessor visited node count: 855/1000000
Preprocessor generated node count: 4702/1500000
Post‐expand include size: 19524/2048000 bytes
Template argument size: 1976/2048000 bytes
Highest expansion depth: 14/40
Expensive parser function count: 5/500
Lua time usage: 0.045/10.000 seconds
Lua memory usage: 1.06 MB/50 MB
-->

<!-- Saved in parser cache with key enwiki:pcache:idhash:2405344-1!*!0!*!*!4!* and timestamp 20140612045447 and revision id 601687338
 -->
