<!-- http%3A//en.wikipedia.org/wiki%3Faction%3Drender%26curid%3D10497504%26oldid%3D601892096 2014-06-16-15-24-28 -->
<div class="thumb tright">
<div class="thumbinner" style="width:402px;"><a href="//en.wikipedia.org/wiki/File:Sensor_sizes_overlaid_inside_-_updated.svg" class="image"><img alt="" src="//upload.wikimedia.org/wikipedia/commons/thumb/9/9a/Sensor_sizes_overlaid_inside_-_updated.svg/400px-Sensor_sizes_overlaid_inside_-_updated.svg.png" width="400" height="327" class="thumbimage" srcset="//upload.wikimedia.org/wikipedia/commons/thumb/9/9a/Sensor_sizes_overlaid_inside_-_updated.svg/600px-Sensor_sizes_overlaid_inside_-_updated.svg.png 1.5x, //upload.wikimedia.org/wikipedia/commons/thumb/9/9a/Sensor_sizes_overlaid_inside_-_updated.svg/800px-Sensor_sizes_overlaid_inside_-_updated.svg.png 2x" data-file-width="550" data-file-height="450" /></a>
<div class="thumbcaption">
<div class="magnify"><a href="//en.wikipedia.org/wiki/File:Sensor_sizes_overlaid_inside_-_updated.svg" class="internal" title="Enlarge"><img src="//bits.wikimedia.org/static-1.24wmf8/skins/common/images/magnify-clip.png" width="15" height="11" alt="" /></a></div>
Comparative dimensions of sensor sizes</div>
</div>
</div>
<p>In <a href="//en.wikipedia.org/wiki/Digital_photography" title="Digital photography">digital photography</a>, the <b>image sensor format</b> is the shape and size of the <a href="//en.wikipedia.org/wiki/Image_sensor" title="Image sensor">image sensor</a>.</p>
<p>The image sensor format of a <a href="//en.wikipedia.org/wiki/Digital_camera" title="Digital camera">digital camera</a> determines the <a href="//en.wikipedia.org/wiki/Angle_of_view" title="Angle of view">angle of view</a> of a particular lens when used with a particular camera. In particular, <a href="//en.wikipedia.org/wiki/Image_sensor" title="Image sensor">image sensors</a> in <a href="//en.wikipedia.org/wiki/Digital_SLR" title="Digital SLR" class="mw-redirect">digital SLR</a> cameras tend to be smaller than the 24&#160;mm&#160;×&#160;36&#160;mm image area of <a href="//en.wikipedia.org/wiki/Full-frame_digital_SLR" title="Full-frame digital SLR">full-frame</a> <a href="//en.wikipedia.org/wiki/35mm" title="35mm" class="mw-redirect">35&#160;mm</a> cameras, and therefore lead to a narrower angle of view.</p>
<p>Lenses produced for <a href="//en.wikipedia.org/wiki/35_mm_film" title="35 mm film">35 mm film</a> <a href="//en.wikipedia.org/wiki/Single-lens_reflex_camera" title="Single-lens reflex camera">cameras</a> may mount well on the digital bodies, but the larger <a href="//en.wikipedia.org/wiki/Image_circle" title="Image circle">image circle</a> of the 35&#160;mm system lens allows unwanted light into the camera body, and the smaller size of the image sensor compared to 35&#160;mm film format results in cropping of the image. This latter effect is known as field of view crop. The format size ratio (relative to the 35&#160;mm film format) is known as the field of view crop factor, <a href="//en.wikipedia.org/wiki/Crop_factor" title="Crop factor">crop factor</a>, lens factor, focal length conversion factor, focal length multiplier or lens multiplier.</p>
<p></p>
<div id="toc" class="toc">
<div id="toctitle">
<h2>Contents</h2>
</div>
<ul>
<li class="toclevel-1 tocsection-1"><a href="#Sensor_size_and_depth_of_field"><span class="tocnumber">1</span> <span class="toctext">Sensor size and depth of field</span></a></li>
<li class="toclevel-1 tocsection-2"><a href="#Sensor_size.2C_noise_and_dynamic_range"><span class="tocnumber">2</span> <span class="toctext">Sensor size, noise and dynamic range</span></a>
<ul>
<li class="toclevel-2 tocsection-3"><a href="#Exposure_and_photon_flux"><span class="tocnumber">2.1</span> <span class="toctext">Exposure and photon flux</span></a></li>
<li class="toclevel-2 tocsection-4"><a href="#Shot_noise"><span class="tocnumber">2.2</span> <span class="toctext">Shot noise</span></a></li>
<li class="toclevel-2 tocsection-5"><a href="#Read_noise"><span class="tocnumber">2.3</span> <span class="toctext">Read noise</span></a></li>
<li class="toclevel-2 tocsection-6"><a href="#Dark_noise"><span class="tocnumber">2.4</span> <span class="toctext">Dark noise</span></a></li>
<li class="toclevel-2 tocsection-7"><a href="#Dynamic_range"><span class="tocnumber">2.5</span> <span class="toctext">Dynamic range</span></a></li>
</ul>
</li>
<li class="toclevel-1 tocsection-8"><a href="#Sensor_size_and_diffraction"><span class="tocnumber">3</span> <span class="toctext">Sensor size and diffraction</span></a></li>
<li class="toclevel-1 tocsection-9"><a href="#Sensor_format_and_lens_size"><span class="tocnumber">4</span> <span class="toctext">Sensor format and lens size</span></a></li>
<li class="toclevel-1 tocsection-10"><a href="#Sensor_size_and_shading_effects"><span class="tocnumber">5</span> <span class="toctext">Sensor size and shading effects</span></a></li>
<li class="toclevel-1 tocsection-11"><a href="#Common_image_sensor_formats"><span class="tocnumber">6</span> <span class="toctext">Common image sensor formats</span></a>
<ul>
<li class="toclevel-2 tocsection-12"><a href="#Medium-format_digital_sensors"><span class="tocnumber">6.1</span> <span class="toctext">Medium-format digital sensors</span></a></li>
<li class="toclevel-2 tocsection-13"><a href="#Sensors_equipping_most_DSLRs_and_mirrorless_interchangeable-lens_cameras"><span class="tocnumber">6.2</span> <span class="toctext">Sensors equipping most DSLRs and mirrorless interchangeable-lens cameras</span></a></li>
<li class="toclevel-2 tocsection-14"><a href="#Sensors_equipping_compact_digital_cameras.2C_mega-zoom_.28bridge.29_cameras_and_camera-phones"><span class="tocnumber">6.3</span> <span class="toctext">Sensors equipping compact digital cameras, mega-zoom (bridge) cameras and camera-phones</span></a></li>
<li class="toclevel-2 tocsection-15"><a href="#Table_of_sensor_formats_and_sizes"><span class="tocnumber">6.4</span> <span class="toctext">Table of sensor formats and sizes</span></a></li>
</ul>
</li>
<li class="toclevel-1 tocsection-16"><a href="#See_also"><span class="tocnumber">7</span> <span class="toctext">See also</span></a></li>
<li class="toclevel-1 tocsection-17"><a href="#Notes_and_references"><span class="tocnumber">8</span> <span class="toctext">Notes and references</span></a></li>
<li class="toclevel-1 tocsection-18"><a href="#External_links"><span class="tocnumber">9</span> <span class="toctext">External links</span></a></li>
</ul>
</div>
<p></p>
<h2><span class="mw-headline" id="Sensor_size_and_depth_of_field">Sensor size and depth of field</span></h2>
<p>Three possible depth of field comparisons between formats are discussed, applying the formulae derived in the article on <a href="//en.wikipedia.org/wiki/Depth_of_field#DOF_vs._format_size_2" title="Depth of field">depth of field</a>. The depths of field of the three cameras may be the same, or different in either order, depending on what is held constant in the comparison.</p>
<p>Considering a picture with the same subject distance and angle of view for two different formats:</p>
<dl>
<dd><img class="mwe-math-fallback-png-inline tex" alt=" \frac {\mathrm{DOF}_2} {\mathrm{DOF}_1} \approx  \frac {d_1} {d_2}" src="//upload.wikimedia.org/math/d/5/c/d5cfefce35b8c0238578195e27bfe7b4.png" /></dd>
</dl>
<p>so the DOFs are in inverse proportion to the absolute <a href="//en.wikipedia.org/wiki/Aperture#In_photography" title="Aperture">aperture diameters</a> <img class="mwe-math-fallback-png-inline tex" alt="d_1" src="//upload.wikimedia.org/math/9/7/5/975e82ee46300a50d901d66c00fe64b1.png" /> and <img class="mwe-math-fallback-png-inline tex" alt="d_2" src="//upload.wikimedia.org/math/2/6/e/26ee688d727ea0c771dbdf3f456895bd.png" />.</p>
<p>Using the same absolute aperture diameter for both formats with the “same picture” criterion (equal angle of view, magnified to same final size) yields the same depth of field. It is equivalent to adjusting the <a href="//en.wikipedia.org/wiki/F-number" title="F-number">f-number</a> inversely in proportion to <a href="//en.wikipedia.org/wiki/Crop_factor" title="Crop factor">crop factor</a> – a smaller f-number for smaller sensors. (This also means that, when holding the shutter speed fixed, the exposure is changed by the adjustment of the f-number required to equalise depth of field. But the aperture area is held constant, so sensors of all sizes receive the same total amount of light energy from the subject. The smaller sensor is then operating at a lower <a href="//en.wikipedia.org/wiki/Film_speed" title="Film speed">ISO setting</a>, by the square of the crop factor.)</p>
<p>And, we might compare the depth of field of sensors receiving the same <a href="//en.wikipedia.org/wiki/Luminous_exposure#Photometric_and_radiometric_exposure" title="Luminous exposure" class="mw-redirect">photometric exposure</a> – the f-number is fixed instead of the aperture diameter – the sensors are operating at the same ISO setting in that case, but the smaller sensor is receiving less total light, by the area ratio. The ratio of depths of field is then</p>
<dl>
<dd><img class="mwe-math-fallback-png-inline tex" alt=" \frac {\mathrm{DOF}_2} {\mathrm{DOF}_1} \approx \frac {l_1} {l_2}" src="//upload.wikimedia.org/math/4/1/b/41b6cee18429bb22e92888af663a4cd2.png" /></dd>
</dl>
<p>where <img class="mwe-math-fallback-png-inline tex" alt=" l_1" src="//upload.wikimedia.org/math/e/6/c/e6c5419e04a1206d2b1ba0ec48009362.png" /> and <img class="mwe-math-fallback-png-inline tex" alt="l_2" src="//upload.wikimedia.org/math/c/7/b/c7b5cb501695b127a4a5203ecdf63d70.png" /> are the characteristic dimensions of the format, and thus <img class="mwe-math-fallback-png-inline tex" alt="l_1/l_2" src="//upload.wikimedia.org/math/9/3/f/93f152ca0b4bc130a8ca63415367fd27.png" /> is the relative crop factor between the sensors. It is this result that gives rise to the common opinion that small sensors yield greater depth of field than large ones.</p>
<p>An alternative is to consider the depth of field given by the same lens in conjunction with different sized sensors (changing the angle of view). The change in depth of field is brought about by the requirement for a different degree of enlargement to achieve the same final image size. In this case the ratio of depths of field becomes</p>
<dl>
<dd><img class="mwe-math-fallback-png-inline tex" alt=" \frac {\mathrm{DOF}_2} {\mathrm{DOF}_1} \approx \frac {l_2} {l_1} " src="//upload.wikimedia.org/math/2/1/b/21b9b3e9ae89df1b3eca9ba8326296a6.png" />.</dd>
</dl>
<h2><span class="mw-headline" id="Sensor_size.2C_noise_and_dynamic_range">Sensor size, noise and dynamic range</span></h2>
<p>Discounting <a href="//en.wikipedia.org/wiki/Fixed-pattern_noise" title="Fixed-pattern noise">pixel response non-uniformity</a> (PRNU), which is not intrinsically sensor-size dependent, the noises in an image sensor are <a href="//en.wikipedia.org/wiki/Shot_noise" title="Shot noise">shot noise</a>, <a href="//en.wikipedia.org/w/index.php?title=Read_noise&amp;action=edit&amp;redlink=1" class="new" title="Read noise (page does not exist)">read noise</a>, and <a href="//en.wikipedia.org/w/index.php?title=Dark_noise&amp;action=edit&amp;redlink=1" class="new" title="Dark noise (page does not exist)">dark noise</a>. The overall <a href="//en.wikipedia.org/wiki/Signal-to-noise_ratio" title="Signal-to-noise ratio">signal to noise ratio</a> of a sensor (SNR), observed at the scale of a single pixel, is</p>
<dl>
<dd><img class="mwe-math-fallback-png-inline tex" alt=" \mathrm{SNR} = \frac{P Q_e t}{\sqrt{P Q_e t + D t + N_r^2}} " src="//upload.wikimedia.org/math/6/d/f/6dfff35afae78b0cfadb55ca311584de.png" /></dd>
</dl>
<p>where <img class="mwe-math-fallback-png-inline tex" alt="P" src="//upload.wikimedia.org/math/4/4/c/44c29edb103a2872f519ad0c9a0fdaaa.png" /> is the incident photon flux (photons per second in the area of a pixel), <img class="mwe-math-fallback-png-inline tex" alt="Q_e" src="//upload.wikimedia.org/math/b/a/2/ba258bc52f435be308bf44bb7b41beb8.png" /> is the <a href="//en.wikipedia.org/wiki/Quantum_efficiency" title="Quantum efficiency">quantum efficiency</a>, <img class="mwe-math-fallback-png-inline tex" alt="t" src="//upload.wikimedia.org/math/e/3/5/e358efa489f58062f10dd7316b65649e.png" /> is the exposure time, <img class="mwe-math-fallback-png-inline tex" alt="D" src="//upload.wikimedia.org/math/f/6/2/f623e75af30e62bbd73d6df5b50bb7b5.png" /> is the pixel dark current in electrons per second and <img class="mwe-math-fallback-png-inline tex" alt="N_r" src="//upload.wikimedia.org/math/5/9/5/595cd2d067a202297f42b0cb7a20cce9.png" /> is the pixel read noise in electrons.<sup id="cite_ref-noise_1-0" class="reference"><a href="#cite_note-noise-1"><span>[</span>1<span>]</span></a></sup></p>
<p>Each of these noises has a different dependency on sensor size.</p>
<h3><span class="mw-headline" id="Exposure_and_photon_flux">Exposure and photon flux</span></h3>
<p>Image sensor noise can be compared across formats for a given fixed photon flux per pixel area (the <i>P</i> in the formulas); this analysis is useful for a fixed number of pixels with pixel area proportional to sensor area, and fixed absolute aperture diameter for a fixed imaging situation in terms of depth of field, diffraction limit at the subject, etc. Or it can be compared for a fixed focal-plane illuminance, corresponding to a fixed <a href="//en.wikipedia.org/wiki/F-number" title="F-number">f-number</a>, in which case <i>P</i> is proportional to pixel area, independent of sensor area. The formulas above and below can be evaluated for either case.</p>
<h3><span class="mw-headline" id="Shot_noise">Shot noise</span></h3>
<p>In the above equation, the shot noise SNR is given by</p>
<dl>
<dd><img class="mwe-math-fallback-png-inline tex" alt="\frac{P Q_e t}{\sqrt{P Q_e t}} = \sqrt{P Q_e t}" src="//upload.wikimedia.org/math/6/0/1/601fc8b8c5fc414e60f134869d5ba4e5.png" />.</dd>
</dl>
<p>Apart from the quantum efficiency it depends on the incident photon flux and the exposure time,which is equivalent to the <a href="//en.wikipedia.org/wiki/Exposure_(photography)" title="Exposure (photography)">exposure</a> and the sensor area; since the exposure is the integration time multiplied with the image plane <a href="//en.wikipedia.org/wiki/Illuminance" title="Illuminance">illuminance</a>, and illuminance is the <a href="//en.wikipedia.org/wiki/Luminous_flux" title="Luminous flux">luminous flux</a> per unit area. Thus for equal exposures, the signal to noise ratios of two different size sensors of equal quantum efficiency and pixel count will (for a given final image size) be in proportion to the square root of the sensor area (or the linear scale factor of the sensor). If the exposure is constrained by the need to achieve some required <a href="//en.wikipedia.org/wiki/Depth_of_field" title="Depth of field">depth of field</a> (with the same shutter speed) then the exposures will be in inverse relation to the sensor area, producing the interesting result that if depth of field is a constraint, image shot noise is not dependent on sensor area.</p>
<h3><span class="mw-headline" id="Read_noise">Read noise</span></h3>
<p>The read noise is the total of all the electronic noises in the conversion chain for the pixels in the sensor array. To compare it with photon noise, it must be referred back to its equivalent in photoelectrons, which requires the division of the noise measured in volts by the conversion gain of the pixel. This is given, for an <a href="//en.wikipedia.org/wiki/Active_pixel_sensor" title="Active pixel sensor">active pixel sensor</a>, by the voltage at the input (gate) of the read transistor divided by the charge which generates that voltage, <img class="mwe-math-fallback-png-inline tex" alt="CG = V_{rt}/Q_{rt}" src="//upload.wikimedia.org/math/3/d/d/3ddfea4a71ecf3ce1c1477d03f075e25.png" />. This is the inverse of the capacitance of the read transistor gate (and the attached floating diffusion) since capacitance <img class="mwe-math-fallback-png-inline tex" alt="C = Q/V" src="//upload.wikimedia.org/math/7/5/f/75f821db74c05ae62d99345bf910bf07.png" />.<sup id="cite_ref-2" class="reference"><a href="#cite_note-2"><span>[</span>2<span>]</span></a></sup> Thus <img class="mwe-math-fallback-png-inline tex" alt="CG = 1/C_{rt}" src="//upload.wikimedia.org/math/5/8/6/5865bc676dc54862e72655c065571f48.png" />.</p>
<p>In general for a planar structure such as a pixel, capacitance is proportional to area, therefore the read noise scales down with sensor area, as long as pixel area scales with sensor area, and that scaling is performed by uniformly scaling the pixel.</p>
<p>Considering the signal to noise ratio due to read noise at a given exposure, the signal will scale as the sensor area along with the read noise and therefore read noise SNR will be unaffected by sensor area. In a depth of field constrained situation, the exposure of the larger sensor will be reduced in proportion to the sensor area, and therefore the read noise SNR will reduce likewise.</p>
<h3><span class="mw-headline" id="Dark_noise">Dark noise</span></h3>
<p>The dark current contributes two kinds of noise: dark offset, which is only partly correlated between pixels, and the shot noise associated with dark offset, which is uncorrelated between pixels. Only the shot-noise component <i>Dt</i> is included in the formula above, since the uncorrelated part of the dark offset is hard to predict, and the correlated or mean part is relatively easy to subtract off. The mean dark current contains contributions proportional both to the area and the linear dimension of the photodiode, with the relative proportions and scale factors depending on the design of the photodiode.<sup id="cite_ref-3" class="reference"><a href="#cite_note-3"><span>[</span>3<span>]</span></a></sup> Thus in general the dark noise of a sensor may be expected to rise as the size of the sensor increases. However, in most sensors the mean pixel dark current at normal temperatures is small, lower than 50 e- per second,<sup id="cite_ref-4" class="reference"><a href="#cite_note-4"><span>[</span>4<span>]</span></a></sup> thus for typical photographic exposure times dark current and its associated noises may be discounted. At very long exposure times, however, it may be a limiting factor. And even at short or medium exposure times, a few outliers in the dark-current distribution may show up as "hot pixels".</p>
<h3><span class="mw-headline" id="Dynamic_range">Dynamic range</span></h3>
<p>Dynamic range is the ratio of the largest and smallest recordable signal, the smallest being typically defined by the 'noise floor'. In the image sensor literature, the noise floor is taken as the readout noise, so <img class="mwe-math-fallback-png-inline tex" alt=" DR = Q_{max} / \sigma_{readout}" src="//upload.wikimedia.org/math/b/8/8/b88a502852879487ffe9d23afbca0e3f.png" /><sup id="cite_ref-5" class="reference"><a href="#cite_note-5"><span>[</span>5<span>]</span></a></sup> (note, the read noise <img class="mwe-math-fallback-png-inline tex" alt="\sigma_{readout}" src="//upload.wikimedia.org/math/c/6/c/c6ce9a10258c399cf701427fa6730fae.png" /> is the same quantity as <img class="mwe-math-fallback-png-inline tex" alt="N_r" src="//upload.wikimedia.org/math/5/9/5/595cd2d067a202297f42b0cb7a20cce9.png" /> referred to in<sup id="cite_ref-noise_1-1" class="reference"><a href="#cite_note-noise-1"><span>[</span>1<span>]</span></a></sup>)</p>
<p>The measurement here is made at the level of a pixel (which strictly means that the DR of sensors with different pixel counts is measured over a different spatial bandwidth, and cannot be compared without normalisation). If we assume sensors with the same pixel count but different sizes, then the pixel area will be in proportion to the sensor area. If the maximum exposure (amount of light per unit area) is the same then both the maximum signal and the read noise reduce in proportion to the pixel (and therefore the sensor) area, so the DR does not change. If the comparison is made according to DOF limited conditions, so that the exposure of the larger sensor is reduced in proportion to the area of the sensor (and pixel, for sensors with equal pixel count) then <img class="mwe-math-fallback-png-inline tex" alt="Q_{max}" src="//upload.wikimedia.org/math/6/8/9/6890ea80b79d7e00b653d6fcdb3e9ea5.png" /> is constant, and the read noise (<img class="mwe-math-fallback-png-inline tex" alt="\sigma_{readout}" src="//upload.wikimedia.org/math/c/6/c/c6ce9a10258c399cf701427fa6730fae.png" />) falls with the sensor area, leading to a higher dynamic range for the smaller sensor. Summarising the above discussion, considering separately the parts of the image signal to noise ratio due to photon shot noise and read noise and their relation to the linear sensor size ratio or 'crop factor' (remembering that conventionally crop factor increases as the sensor gets smaller) then:</p>
<table class="wikitable">
<tr>
<th></th>
<th>Shot noise SNR</th>
<th>Read noise SNR</th>
<th>Dynamic range</th>
</tr>
<tr>
<td>Fixed exposure</td>
<td>Inversely proportional to crop factor</td>
<td>No change</td>
<td>No change</td>
</tr>
<tr>
<td>DOF constrained</td>
<td>No change</td>
<td>Proportional to square of crop factor</td>
<td>Proportional to square of crop factor</td>
</tr>
</table>
<p>It should be noted that this discussion isolates the effects of sensor scale on SNR and DR, in reality there are many other factors which affect both these quantities.</p>
<h2><span class="mw-headline" id="Sensor_size_and_diffraction">Sensor size and diffraction</span></h2>
<p>The resolution of all optical systems is limited by <a href="//en.wikipedia.org/wiki/Diffraction" title="Diffraction">diffraction</a>. One way of considering the effect that diffraction has on cameras using different sized sensors is to consider the <a href="//en.wikipedia.org/wiki/Optical_transfer_function" title="Optical transfer function">modulation transfer function</a> (MTF) due to diffraction, which will contribute a factor to the overall system MTF along with the other factors, typically the MTFs of the lens, anti-aliasing filter and sensor sampling window.<sup id="cite_ref-LLResolution_6-0" class="reference"><a href="#cite_note-LLResolution-6"><span>[</span>6<span>]</span></a></sup> The spatial cut-off frequency due to diffraction through a lens aperture is</p>
<dl>
<dd><img class="mwe-math-fallback-png-inline tex" alt="\xi_\mathrm{cutoff}=\frac{1}{\lambda N}" src="//upload.wikimedia.org/math/a/8/7/a876d74d62be61c3c0d5f3d237c42e69.png" /></dd>
</dl>
<p>where λ is the wavelength of the light passing through the system and N is the <a href="//en.wikipedia.org/wiki/F-number" title="F-number">f-number</a> of the lens. If that aperture is circular, as are (approximately) most photographic apertures, then the MTF is given by</p>
<dl>
<dd><img class="mwe-math-fallback-png-inline tex" alt="\mathrm{MTF}(\xi / \xi_\mathrm{cutoff})=\frac{2}{\pi} \left \{ \cos^{-1}(\xi / \xi_\mathrm{cutoff})-(\xi / \xi_\mathrm{cutoff})\left [ 1-( \xi / \xi_\mathrm{cutoff})^2 \right ]^{1/2}  \right \}" src="//upload.wikimedia.org/math/c/f/5/cf56cfe72db860e479ff1f395f9c830b.png" /></dd>
</dl>
<p>for <img class="mwe-math-fallback-png-inline tex" alt=" \xi &lt; \xi_\mathrm{cutoff} " src="//upload.wikimedia.org/math/8/1/a/81a6c07377850afee95968e8a9edfd73.png" /> and <img class="mwe-math-fallback-png-inline tex" alt=" 0 " src="//upload.wikimedia.org/math/c/f/c/cfcd208495d565ef66e7dff9f98764da.png" /> for <img class="mwe-math-fallback-png-inline tex" alt=" \xi \ge \xi_\mathrm{cutoff} " src="//upload.wikimedia.org/math/3/b/b/3bbf417973cf1307a3412d3f4e29e5cb.png" /><sup id="cite_ref-DiffractionMTF_7-0" class="reference"><a href="#cite_note-DiffractionMTF-7"><span>[</span>7<span>]</span></a></sup> The diffraction based factor of the system MTF will therefore scale according to <img class="mwe-math-fallback-png-inline tex" alt="\xi_\mathrm{cutoff}" src="//upload.wikimedia.org/math/7/a/6/7a691c869084f4cd1b87b30af8587227.png" /> and in turn according to <img class="mwe-math-fallback-png-inline tex" alt=" 1/N " src="//upload.wikimedia.org/math/4/5/2/452ceee64d94ae0613dca3bcf3eb3322.png" /> (for the same light wavelength).</p>
<p>In considering the effect of sensor size, and its effect on the final image, the different magnification required to obtain the same size image for viewing must be accounted for, resulting in an additional scale factor of <img class="mwe-math-fallback-png-inline tex" alt="1/{C}" src="//upload.wikimedia.org/math/f/b/1/fb19fb04e4af59a722d04d4c88063924.png" /> where <img class="mwe-math-fallback-png-inline tex" alt="{C}" src="//upload.wikimedia.org/math/c/e/7/ce75558ea969dc0e0f4848f576eb7b8d.png" /> is the relative crop factor, making the overall scale factor <img class="mwe-math-fallback-png-inline tex" alt="1 / (N C)" src="//upload.wikimedia.org/math/9/0/2/902b174cbc4a177468cf1aad87e5bb26.png" />. Considering the three cases above:</p>
<p>For the 'same picture' conditions, same angle of view, subject distance and depth of field, then the F-numbers are in the ratio <img class="mwe-math-fallback-png-inline tex" alt="1/C" src="//upload.wikimedia.org/math/3/d/1/3d13613a9043582791618385ead7d49d.png" />, so the scale factor for the diffraction MTF is 1, leading to the conclusion that the diffraction MTF at a given depth of field is independent of sensor size.</p>
<p>In both the 'same photometric exposure' and 'same lens' conditions, the F-number is not changed, and thus the spatial cutoff and resultant MTF on the sensor is unchanged, leaving the MTF in the viewed image to be scaled as the magnification, or inversely as the crop factor.</p>
<h2><span class="mw-headline" id="Sensor_format_and_lens_size">Sensor format and lens size</span></h2>
<p>It might be expected that lenses appropriate for a range of sensor sizes could be produced by simply scaling the same designs in proportion to the crop factor.<sup id="cite_ref-8" class="reference"><a href="#cite_note-8"><span>[</span>8<span>]</span></a></sup> Such an exercise would in theory produce a lens with the same F-number and angle of view, with a size proportional to the sensor crop factor. In practice, simple scaling of lens designs is not always achievable, due to factors such as the non-scalability of manufacturing tolerance, structural integrity of glass lenses of different sizes and available manufacturing techniques and costs. Moreover, to maintain the same absolute amount of information in an image (which can be measured as the space bandwidth product<sup id="cite_ref-9" class="reference"><a href="#cite_note-9"><span>[</span>9<span>]</span></a></sup>) the lens for a smaller sensor requires a greater resolving power. The development of the 'Tessar' lens is discussed by Nasse,<sup id="cite_ref-10" class="reference"><a href="#cite_note-10"><span>[</span>10<span>]</span></a></sup> and shows its transformation from an f/6.3 lens for plate cameras using the original three-group configuration through to an f/2.8 5.2&#160;mm four-element optic with eight extremely aspheric surfaces, economically manufacturable because of its small size. Its performance is 'better than the best 35&#160;mm lenses – but only for a very small image'.</p>
<p>In summary, as sensor size reduces, the accompanying lens designs will change, often quite radically, to take advantage of manufacturing techniques made available due to the reduced size. The functionality of such lenses can also take advantage of these, with extreme zoom ranges becoming possible. These lenses are often very large in relation to sensor size, but with a small sensor can be fitted into a compact package.</p>
<p>Small body means small lens and means small sensor, so to keep <a href="//en.wikipedia.org/wiki/Smartphone" title="Smartphone">smartphones</a> slim and light, the smartphone manufacturers use tiny sensor usually less than 1/2.3" which usually use in most <a href="//en.wikipedia.org/wiki/Bridge_camera" title="Bridge camera">Bridge cameras</a> and until now only <a href="//en.wikipedia.org/wiki/Nokia_808_PureView" title="Nokia 808 PureView">Nokia 808 PureView</a> uses 1/1.2" sensor which sensor size almost three times of 1/2.3" sensor size. To use bigger sensor has advantage of better image quality, but with sensor technology improves, small sensor initial to catch larger sensor, although the bigger sensor always on the lead, but not all of the consumers need superb photo quality or bigger sensor as we can see that smartphone has toppled low and middle end compact camera sales and uses for taking images.<sup id="cite_ref-11" class="reference"><a href="#cite_note-11"><span>[</span>11<span>]</span></a></sup></p>
<h2><span class="mw-headline" id="Sensor_size_and_shading_effects">Sensor size and shading effects</span></h2>
<p>Semiconductor image sensors can suffer from shading effects at large apertures and at the periphery of the image field, due to the geometry of the light cone projected from the exit pupil of the lens to a point, or pixel, on the sensor surface. The effects are discussed in detail by Catrysse and Wandell .<sup id="cite_ref-Catrysse_12-0" class="reference"><a href="#cite_note-Catrysse-12"><span>[</span>12<span>]</span></a></sup> In the context of this discussion the most important result from the above is that to ensure a full transfer of light energy between two coupled optical systems such as the lens' exit pupil to a pixel's photoreceptor the <a href="//en.wikipedia.org/wiki/Etendue" title="Etendue">geometrical extent</a> (also known as etendue or light throughput) of the objective lens / pixel system must be smaller than or equal to the geometrical extent of the microlens / photoreceptor system. The geometrical extent of the objective lens / pixel system is given by</p>
<dl>
<dd><img class="mwe-math-fallback-png-inline tex" alt=" G_\mathrm{objective} \simeq \frac{w_\mathrm{pixel}}{2{(f/\#)}_\mathrm{objective}} " src="//upload.wikimedia.org/math/0/e/a/0ea7fa6c0127bed7556e750c74d7be71.png" />,</dd>
</dl>
<p>where <span class="texhtml"><var>w<sub>pixel</sub></var></span> is the width of the pixel and <span class="texhtml"><var>(f/#)<sub>objective</sub></var></span> is the f-number of the objective lens. The geometrical extent of the microlens / photoreceptor system is given by</p>
<dl>
<dd><img class="mwe-math-fallback-png-inline tex" alt=" G_\mathrm{pixel} \simeq \frac{w_\mathrm{photoreceptor}}{2{(f/\#)}_\mathrm{microlens}} " src="//upload.wikimedia.org/math/1/e/d/1ed5aa0df5941eb2ba70d79429fb4b06.png" />,</dd>
</dl>
<p>where <span class="texhtml"><var>w<sub>photoreceptor</sub></var></span> is the width of the photoreceptor and <span class="texhtml"><var>(f/#)<sub>microlens</sub></var></span> is the f-number of the microlens.</p>
<p>So to avoid shading,</p>
<dl>
<dd><img class="mwe-math-fallback-png-inline tex" alt=" G_\mathrm{pixel} \ge  G_\mathrm{objective}" src="//upload.wikimedia.org/math/c/d/3/cd32491aa95366e033f3357dfade33d4.png" />, therefore <img class="mwe-math-fallback-png-inline tex" alt=" \frac{w_\mathrm{photoreceptor}}{{(f/\#)}_\mathrm{microlens}} \ge \frac{w_\mathrm{pixel}}{{(f/\#)}_\mathrm{objective}}" src="//upload.wikimedia.org/math/2/b/f/2bfc3a20ec725f2306820bf23c92a44a.png" /></dd>
</dl>
<p>If <span class="texhtml"><var>w<sub>photoreceptor</sub></var> / <var>w<sub>pixel</sub></var> = <var>ff</var></span>, the linear fill factor of the lens, then the condition becomes</p>
<dl>
<dd><img class="mwe-math-fallback-png-inline tex" alt=" {(f/\#)}_\mathrm{microlens} \le {(f/\#)}_\mathrm{objective} \times \mathit{ff}" src="//upload.wikimedia.org/math/d/0/c/d0cdb57b17e30abf9dd016af4e052724.png" /></dd>
</dl>
<p>Thus if shading is to be avoided the f-number of the microlens must be smaller than the f-number of the taking lens by at least a factor equal to the linear fill factor of the pixel. The f-number of the microlens is determined ultimately by the width of the pixel and its height above the silicon, which determines its focal length. In turn, this is determined by the height of the metallisation layers, also known as the 'stack height'. For a given stack height, the f-number of the microlenses will increase as pixel size reduces, and thus the objective lens f-number at which shading occurs will tend to increase. This effect has been observed in practice, as recorded in the DxOmark article 'F-stop blues'<sup id="cite_ref-13" class="reference"><a href="#cite_note-13"><span>[</span>13<span>]</span></a></sup></p>
<p>In order to maintain pixel counts smaller sensors will tend to have smaller pixels, while at the same time smaller objective lens f-numbers are required to maximise the amount of light projected on the sensor. To combat the effect discussed above, smaller format pixels include engineering design features to allow the reduction in f-number of their microlenses. These may include simplified pixel designs which require less metallisation, 'light pipes' built within the pixel to bring its apparent surface closer to the microlens and '<a href="//en.wikipedia.org/wiki/Back-illuminated_sensor" title="Back-illuminated sensor">back side illumination</a>' in which the wafer is thinned to expose the rear of the photodetectors and the microlens layer is placed directly on that surface, rather than the front side with its wiring layers. The relative effectiveness of these stratagems is discussed by <a href="//en.wikipedia.org/wiki/Aptina" title="Aptina">Aptina</a> in some detail.<sup id="cite_ref-14" class="reference"><a href="#cite_note-14"><span>[</span>14<span>]</span></a></sup></p>
<h2><span class="mw-headline" id="Common_image_sensor_formats">Common image sensor formats</span></h2>
<div class="thumb tright">
<div class="thumbinner" style="width:302px;"><a href="//en.wikipedia.org/wiki/File:SensorSizes.svg" class="image"><img alt="" src="//upload.wikimedia.org/wikipedia/commons/thumb/9/95/SensorSizes.svg/300px-SensorSizes.svg.png" width="300" height="420" class="thumbimage" srcset="//upload.wikimedia.org/wikipedia/commons/thumb/9/95/SensorSizes.svg/450px-SensorSizes.svg.png 1.5x, //upload.wikimedia.org/wikipedia/commons/thumb/9/95/SensorSizes.svg/600px-SensorSizes.svg.png 2x" data-file-width="500" data-file-height="700" /></a>
<div class="thumbcaption">
<div class="magnify"><a href="//en.wikipedia.org/wiki/File:SensorSizes.svg" class="internal" title="Enlarge"><img src="//bits.wikimedia.org/static-1.24wmf8/skins/common/images/magnify-clip.png" width="15" height="11" alt="" /></a></div>
Sizes of the sensors used in most current digital cameras relative to a standard 35mm frame.</div>
</div>
</div>
<h3><span class="mw-headline" id="Medium-format_digital_sensors">Medium-format digital sensors</span></h3>
<p>The most common sensor size for medium-format digital cameras is approximately 48&#160;mm ×&#160;36&#160;mm (1.9&#160;in ×&#160;1.4&#160;in)<sup class="noprint Inline-Template Template-Fact" style="white-space:nowrap;">[<i><a href="//en.wikipedia.org/wiki/Wikipedia:Citation_needed" title="Wikipedia:Citation needed"><span title="This claim needs references to reliable sources. (June 2010)">citation needed</span></a></i>]</sup>, due to the widespread use of <a href="//en.wikipedia.org/wiki/Kodak" title="Kodak" class="mw-redirect">Kodak</a>'s 22-<a href="//en.wikipedia.org/wiki/Megapixel" title="Megapixel" class="mw-redirect">megapixel</a> KAF-22000 and 39-megapixel KAF-39000<sup id="cite_ref-15" class="reference"><a href="#cite_note-15"><span>[</span>15<span>]</span></a></sup> CCDs in that format. <a href="//en.wikipedia.org/wiki/Phase_One_(company)" title="Phase One (company)">Phase one</a> offers the P65+ digital back with <a href="//en.wikipedia.org/wiki/Dalsa" title="Dalsa">Dalsa</a>'s 53.9&#160;mm ×&#160;40.4&#160;mm (2.12&#160;in ×&#160;1.59&#160;in) sensor containing 60.5 megapixels<sup id="cite_ref-16" class="reference"><a href="#cite_note-16"><span>[</span>16<span>]</span></a></sup> and <a href="//en.wikipedia.org/wiki/Leica_Camera" title="Leica Camera">Leica</a> offers an "S-System" DSLR with a 45&#160;mm ×&#160;30&#160;mm (1.8&#160;in ×&#160;1.2&#160;in) sensor containing 37-megapixels.<sup id="cite_ref-17" class="reference"><a href="#cite_note-17"><span>[</span>17<span>]</span></a></sup> In 2010, <a href="//en.wikipedia.org/wiki/Pentax" title="Pentax">Pentax</a> released the 40MP 645D medium format DSLR with a 44&#160;mm ×&#160;33&#160;mm (1.7&#160;in ×&#160;1.3&#160;in) sensor.<sup id="cite_ref-18" class="reference"><a href="#cite_note-18"><span>[</span>18<span>]</span></a></sup></p>
<h3><span class="mw-headline" id="Sensors_equipping_most_DSLRs_and_mirrorless_interchangeable-lens_cameras">Sensors equipping most DSLRs and mirrorless interchangeable-lens cameras</span></h3>
<p>Some professional DSLRs use full-frame sensors, equal to the size of a frame of 35&#160;mm film.</p>
<p>Most consumer-level DSLRs and <a href="//en.wikipedia.org/wiki/Mirrorless_interchangeable-lens_camera" title="Mirrorless interchangeable-lens camera">MILCs/EVILs</a> use relatively large sensors, either around the size of a frame of <a href="//en.wikipedia.org/wiki/Advanced_Photo_System" title="Advanced Photo System">APS</a>-C film, with a <a href="//en.wikipedia.org/wiki/Crop_factor" title="Crop factor">crop factor</a> of 1.5-1.6; or 30% smaller than that, with a crop factor of 2.0 (this is the <a href="//en.wikipedia.org/wiki/Four_Thirds_System" title="Four Thirds System" class="mw-redirect">Four Thirds System</a>, adopted by <a href="//en.wikipedia.org/wiki/Olympus_(company)" title="Olympus (company)" class="mw-redirect">Olympus</a> and <a href="//en.wikipedia.org/wiki/Panasonic_Corporation" title="Panasonic Corporation" class="mw-redirect">Panasonic</a>).</p>
<p>On September 2011, Nikon announced their new format CX, whose size is 1" (2.7 crop factor).<sup id="cite_ref-19" class="reference"><a href="#cite_note-19"><span>[</span>19<span>]</span></a></sup> It has been adopted for the Nikon 1 camera system (Nikon J1 and V1 models), and subsequently, Sony released the pocket size <a href="//en.wikipedia.org/wiki/Cyber-shot_DSC-RX100" title="Cyber-shot DSC-RX100" class="mw-redirect">Cyber-shot DSC-RX100</a> digital camera, in 2012, which also uses a 1" sensor.</p>
<p>As of November 2013<sup class="plainlinks noprint asof-tag update" style="display:none;"><a class="external text" href="//en.wikipedia.org/w/index.php?title=Image_sensor_format&amp;action=edit">[update]</a></sup> there is only one MILC model equipped with a very small sensor, typical of compact cameras: it is the <a href="//en.wikipedia.org/wiki/Pentax_Q#Pentax_Q7" title="Pentax Q" class="mw-redirect">Pentax Q7</a>, equipped with a 1/1.7" sensor (4.55 crop factor). See <a href="#Sensors_equipping_compact_digital_cameras_and_camera-phones">Sensors equipping Compact digital cameras and camera-phones</a> section below. Many different terms are used in marketing to describe DSLR/MILC sensor formats, including the following:</p>
<ul>
<li><a href="//en.wikipedia.org/wiki/Full-frame_digital_SLR" title="Full-frame digital SLR">Full-frame digital SLR</a> format, with sensor dimensions nearly equal to those of <a href="//en.wikipedia.org/wiki/135_film" title="135 film">35 mm</a> film (36 × 24&#160;mm)</li>
<li><a href="//en.wikipedia.org/wiki/Canon_(company)" title="Canon (company)" class="mw-redirect">Canon</a>'s APS-H format for high-speed pro-level DSLRs (crop factor 1.3)</li>
<li><a href="//en.wikipedia.org/wiki/Leica_Camera" title="Leica Camera">Leica</a>'s <a href="//en.wikipedia.org/wiki/Leica_M8" title="Leica M8">M8 and M8.2</a> sensor (crop factor 1.33).</li>
<li><a href="//en.wikipedia.org/wiki/APS-C" title="APS-C">APS-C</a> refers to a range of similarly-sized formats, including
<ul>
<li><a href="//en.wikipedia.org/wiki/Nikon" title="Nikon">Nikon</a>, <a href="//en.wikipedia.org/wiki/Pentax" title="Pentax">Pentax</a>, <a href="//en.wikipedia.org/wiki/Samsung" title="Samsung">Samsung</a>, <a href="//en.wikipedia.org/wiki/Konica_Minolta" title="Konica Minolta">Konica Minolta</a>, <a href="//en.wikipedia.org/wiki/Sony" title="Sony">Sony</a>, <a href="//en.wikipedia.org/wiki/Fujifilm" title="Fujifilm">Fujifilm</a>, <a href="//en.wikipedia.org/wiki/Epson_R-D1" title="Epson R-D1">Epson</a>, <a href="//en.wikipedia.org/wiki/Sigma" title="Sigma">Sigma</a> (crop factor 1.5)</li>
<li><a href="//en.wikipedia.org/wiki/Canon_(company)" title="Canon (company)" class="mw-redirect">Canon</a> (crop factor 1.6)</li>
</ul>
</li>
<li><a href="//en.wikipedia.org/wiki/Foveon_X3" title="Foveon X3" class="mw-redirect">Foveon X3</a> format used in <a href="//en.wikipedia.org/wiki/Sigma_Corporation" title="Sigma Corporation">Sigma</a> SD-series DSLRs and DP-series mirrorless (crop factor 1.7) (latest models include <a href="//en.wikipedia.org/wiki/Sigma_SD1" title="Sigma SD1">SD1</a>, <a href="//en.wikipedia.org/w/index.php?title=Sigma_DP2_Merrill&amp;action=edit&amp;redlink=1" class="new" title="Sigma DP2 Merrill (page does not exist)">DP2 Merrill</a> use crop factor 1.5 foveon sensor)</li>
<li><a href="//en.wikipedia.org/wiki/Four_Thirds_System" title="Four Thirds System" class="mw-redirect">Four Thirds System</a> and <a href="//en.wikipedia.org/wiki/Micro_Four_Thirds_System" title="Micro Four Thirds System" class="mw-redirect">Micro Four Thirds System</a> format (crop factor 2.0)</li>
<li><a href="//en.wikipedia.org/wiki/Nikon_CX_format" title="Nikon CX format">Nikon CX format</a> used in <a href="//en.wikipedia.org/wiki/Nikon_1_series" title="Nikon 1 series">Nikon 1 series</a> (crop factor 2.7)</li>
</ul>
<p>When <a href="//en.wikipedia.org/wiki/Full-frame_digital_SLR" title="Full-frame digital SLR">full-frame</a> sensors were first introduced, production costs could exceed twenty times the cost of an APS-C sensor. Only twenty full-frame sensors can be produced on an 8 inches (20&#160;cm) silicon wafer that would fit 100 or more APS-C sensors, and there is a significant reduction in <a href="//en.wikipedia.org/wiki/Semiconductor_device_fabrication" title="Semiconductor device fabrication">yield</a> due to the large area for contaminants per component. Additionally, the full frame sensor originally required three separate exposures during the <a href="//en.wikipedia.org/wiki/Photolithography" title="Photolithography">photolithography</a> stage, which requires separate masks and quality control steps. The APS-H size was selected since it was then the largest that could be imaged with a single mask to help control production costs and manage yields.<sup id="cite_ref-canon-wp_20-0" class="reference"><a href="#cite_note-canon-wp-20"><span>[</span>20<span>]</span></a></sup> Newer photolithography equipment now allows single-pass exposures for full-frame sensors, although other size-related production constraints remain much the same.</p>
<p>Due to the ever-changing constraints of <a href="//en.wikipedia.org/wiki/Semiconductor_fabrication" title="Semiconductor fabrication" class="mw-redirect">semiconductor fabrication</a> and processing, and because camera manufacturers often source sensors from third-party <a href="//en.wikipedia.org/wiki/Semiconductor_foundry" title="Semiconductor foundry" class="mw-redirect">foundries</a>, it is common for sensor dimensions to vary slightly within the same nominal format. For example, the <a href="//en.wikipedia.org/wiki/Nikon" title="Nikon">Nikon</a> <a href="//en.wikipedia.org/wiki/Nikon_D3" title="Nikon D3">D3</a> and <a href="//en.wikipedia.org/wiki/Nikon_D700" title="Nikon D700">D700</a> cameras' nominally full-frame sensors actually measure 36 × 23.9&#160;mm, slightly smaller than a 36 × 24&#160;mm frame of 35&#160;mm film. As another example, the <a href="//en.wikipedia.org/wiki/Pentax" title="Pentax">Pentax</a> <a href="//en.wikipedia.org/wiki/Pentax_K200D" title="Pentax K200D">K200D</a>'s sensor (made by <a href="//en.wikipedia.org/wiki/Sony" title="Sony">Sony</a>) measures 23.5 × 15.7&#160;mm, while the contemporaneous <a href="//en.wikipedia.org/wiki/Pentax_K20D" title="Pentax K20D">K20D</a>'s sensor (made by <a href="//en.wikipedia.org/wiki/Samsung_Techwin" title="Samsung Techwin">Samsung</a>) measures 23.4 × 15.6&#160;mm.</p>
<p>Most DSLR image sensor formats approximate the 3:2 <a href="//en.wikipedia.org/wiki/Aspect_ratio" title="Aspect ratio">aspect ratio</a> of 35&#160;mm film. Again, the <a href="//en.wikipedia.org/wiki/Four_Thirds_System" title="Four Thirds System" class="mw-redirect">Four Thirds System</a> is a notable exception, with an aspect ratio of 4:3 as seen in most compact digital cameras (see below).</p>
<h3><span class="mw-headline" id="Sensors_equipping_compact_digital_cameras.2C_mega-zoom_.28bridge.29_cameras_and_camera-phones">Sensors equipping compact digital cameras, mega-zoom (bridge) cameras and camera-phones</span></h3>
<p>Most image sensors equipping compact cameras have an <a href="//en.wikipedia.org/wiki/Aspect_ratio" title="Aspect ratio">aspect ratio</a> of 4:3. This matches the aspect ratio of the popular <a href="//en.wikipedia.org/wiki/SVGA" title="SVGA" class="mw-redirect">SVGA</a>, <a href="//en.wikipedia.org/wiki/XGA" title="XGA" class="mw-redirect">XGA</a>, and <a href="//en.wikipedia.org/wiki/SXGA" title="SXGA" class="mw-redirect">SXGA</a> display resolutions at the time of the first digital cameras, allowing images to be displayed on usual <a href="//en.wikipedia.org/wiki/Computer_monitor" title="Computer monitor">monitors</a> without cropping.</p>
<p>As of December 2010<sup class="plainlinks noprint asof-tag update" style="display:none;"><a class="external text" href="//en.wikipedia.org/w/index.php?title=Image_sensor_format&amp;action=edit">[update]</a></sup> most compact digital cameras used small 1/2.3" sensors. Such cameras include Canon Powershot SX230 IS, Fuji Finepix Z90 and Nikon Coolpix S9100. Some older <a href="//en.wikipedia.org/wiki/Digital_camera" title="Digital camera">digital cameras</a> (mostly from 2005–2010) used a tiny 1/2.5" sensor: these include Panasonic Lumix DMC-FS62, Canon Powershot SX120 IS, <a href="//en.wikipedia.org/w/index.php?title=Sony_Cyber-shot_DSC-S700&amp;action=edit&amp;redlink=1" class="new" title="Sony Cyber-shot DSC-S700 (page does not exist)">Sony Cyber-shot DSC-S700</a>, and Casio Exilim EX-Z80.</p>
<p>High-end compact cameras using sensors of nearly twice the area than sensors equipping common compacts include Canon PowerShot G12 (1/1.7") and Powershot S90/<a href="//en.wikipedia.org/wiki/Canon_PowerShot_S95" title="Canon PowerShot S95">S95</a> (1/1.7"), Ricoh GR Digital IV (1/1.7"), Nikon Coolpix P7100 (1/1.7"), Samsung EX1 (1/1.7"), Panasonic DMC-LX5 (1/1.63") and Olympus XZ-1(1/1.63"). Fujifilm FinePix X-10 has a 2/3" sensor, the largest sensor on camera small enough to be labelled as compact (despite weighing 353 grams) until June 2012. That is until Sony announced DSC-RX100, a real compact (weight: 213 grams) equipped with a 1" sensor (i.e. one only used on MILCs until then). Actually, Canon labels "compact camera" its PowerShot G1 X, equipped with a huge 1.5" sensor (i.e. a sensor larger than the 4/3" sensors equipping some compact DSLR). Nonetheless, weighing well over half a kilo (534 grams) G1 X is arguably a <a href="//en.wikipedia.org/wiki/Bridge_camera" title="Bridge camera">bridge camera</a> rather than a compact.<sup class="noprint Inline-Template Template-Fact" style="white-space:nowrap;">[<i><a href="//en.wikipedia.org/wiki/Wikipedia:Citation_needed" title="Wikipedia:Citation needed"><span title="This claim needs references to reliable sources. (June 2013)">citation needed</span></a></i>]</sup></p>
<div class="thumb tright">
<div class="thumbinner" style="width:402px;"><a href="//en.wikipedia.org/wiki/File:Digital_camera_sensor_area.svg" class="image"><img alt="" src="//upload.wikimedia.org/wikipedia/commons/thumb/3/31/Digital_camera_sensor_area.svg/400px-Digital_camera_sensor_area.svg.png" width="400" height="320" class="thumbimage" srcset="//upload.wikimedia.org/wikipedia/commons/thumb/3/31/Digital_camera_sensor_area.svg/600px-Digital_camera_sensor_area.svg.png 1.5x, //upload.wikimedia.org/wikipedia/commons/thumb/3/31/Digital_camera_sensor_area.svg/800px-Digital_camera_sensor_area.svg.png 2x" data-file-width="600" data-file-height="480" /></a>
<div class="thumbcaption">
<div class="magnify"><a href="//en.wikipedia.org/wiki/File:Digital_camera_sensor_area.svg" class="internal" title="Enlarge"><img src="//bits.wikimedia.org/static-1.24wmf8/skins/common/images/magnify-clip.png" width="15" height="11" alt="" /></a></div>
For many years until Sep. 2011 a gap existed between compact digital and DSLR camera sensor sizes. The x axis is a discrete set of sensor format sizes used in digital cameras, not a linear measurement axis.</div>
</div>
</div>
<p>As of 2012<sup class="plainlinks noprint asof-tag update" style="display:none;"><a class="external text" href="//en.wikipedia.org/w/index.php?title=Image_sensor_format&amp;action=edit">[update]</a></sup> most <a href="//en.wikipedia.org/wiki/Bridge_camera" title="Bridge camera">bridge cameras</a>, including the <a href="//en.wikipedia.org/w/index.php?title=Sony_Cyber-shot_DSC-HX100V&amp;action=edit&amp;redlink=1" class="new" title="Sony Cyber-shot DSC-HX100V (page does not exist)">Sony Cyber-shot DSC-HX100V</a> and the Canon PowerShot SX40 HS, use a small 1/2.3" sensor (i.e. same size as those used in common compact cameras). The high-end bridge camera Fuji XS-1, though, is equipped with a much larger sensor (2/3" – twice the area of a 1/2.3" sensor: see table further on).</p>
<p>The sensors of <a href="//en.wikipedia.org/wiki/Camera_phone" title="Camera phone">camera phones</a> are typically much smaller than those of typical compact cameras, allowing greater miniaturization of the electrical and optical components. Sensor sizes of around 1/6" are common in camera phones, <a href="//en.wikipedia.org/wiki/Webcam" title="Webcam">webcams</a> and <a href="//en.wikipedia.org/wiki/Digital_camcorder" title="Digital camcorder" class="mw-redirect">digital camcorders</a>. The <a href="//en.wikipedia.org/wiki/Nokia_N8" title="Nokia N8">Nokia N8</a>'s 1/1.83" sensor was the largest in a phone in late 2011. The <a href="//en.wikipedia.org/wiki/Nokia_808" title="Nokia 808" class="mw-redirect">Nokia 808</a> surpasses compact cameras with its 41 million pixels, 1/1.2" sensor.<sup id="cite_ref-21" class="reference"><a href="#cite_note-21"><span>[</span>21<span>]</span></a></sup></p>
<h3><span class="mw-headline" id="Table_of_sensor_formats_and_sizes">Table of sensor formats and sizes</span></h3>
<p>Sensor formats of digital cameras are mostly expressed in the non-standardized "inch" system as approximately 1.5 times the length of the diagonal of the sensor. This goes back to the way image sizes of video cameras used until the late 1980s were expressed, referring to the outside diameter of the glass envelope of the <a href="//en.wikipedia.org/wiki/Video_camera_tube" title="Video camera tube">video camera tube</a>. <a href="//en.wikipedia.org/wiki/David_Pogue" title="David Pogue">David Pogue</a> of <i>The New York Times</i> states that "the actual sensor size is much smaller than what the camera companies publish – about one-third smaller." For example, a camera advertising a 1/2.7" sensor does not have a sensor with a diagonal of 0.37"; instead, the diagonal is closer to 0.26".<sup id="cite_ref-22" class="reference"><a href="#cite_note-22"><span>[</span>22<span>]</span></a></sup><sup id="cite_ref-dpreview-sensor-sizes_23-0" class="reference"><a href="#cite_note-dpreview-sensor-sizes-23"><span>[</span>23<span>]</span></a></sup> Instead of "formats", these sensor sizes are often called <i>types</i>, as in "1/2-inch-type CCD."</p>
<p>Due to inch-based sensor formats being not standardized, their exact dimensions may vary, but those listed are typical.<sup id="cite_ref-dpreview-sensor-sizes_23-1" class="reference"><a href="#cite_note-dpreview-sensor-sizes-23"><span>[</span>23<span>]</span></a></sup> The listed sensor areas span more than a factor of 1000 and are <a href="//en.wikipedia.org/wiki/Proportionality_(mathematics)" title="Proportionality (mathematics)">proportional</a> to the maximum possible collection of light and <a href="//en.wikipedia.org/wiki/Image_resolution" title="Image resolution">image resolution</a> (same <a href="//en.wikipedia.org/wiki/Lens_speed" title="Lens speed">lens speed</a>, i.e., minimum <a href="//en.wikipedia.org/wiki/F-number" title="F-number">F-number</a>), but in practice are not directly proportional to <a href="//en.wikipedia.org/wiki/Image_noise" title="Image noise">image noise</a> or resolution due to other limitations. See comparisons.<sup id="cite_ref-dxoa_24-0" class="reference"><a href="#cite_note-dxoa-24"><span>[</span>24<span>]</span></a></sup><sup id="cite_ref-imac_25-0" class="reference"><a href="#cite_note-imac-25"><span>[</span>25<span>]</span></a></sup> Film format sizes are included for comparison.</p>
<table style="width: 100%; text-align: center;" border="1" cellspacing="0" class="wikitable sortable">
<tr>
<th>Type</th>
<th>Diagonal (mm)</th>
<th>Width (mm)</th>
<th>Height (mm)</th>
<th>Area (mm<sup>2</sup>)</th>
<th><a href="//en.wikipedia.org/wiki/F-number#Stops.2C_f-stop_conventions.2C_and_exposure" title="F-number">Stops</a> (area)</th>
<th><a href="//en.wikipedia.org/wiki/Crop_factor" title="Crop factor">Crop factor</a><sup id="cite_ref-26" class="reference"><a href="#cite_note-26"><span>[</span>26<span>]</span></a></sup></th>
</tr>
<tr>
<td>1/10"</td>
<td>1.60</td>
<td>1.28</td>
<td>0.96</td>
<td>1.23</td>
<td>-9.51</td>
<td>27.04</td>
</tr>
<tr>
<td>1/8"</td>
<td>2.00</td>
<td>1.60</td>
<td>1.20</td>
<td>1.92</td>
<td>-8.81</td>
<td>21.65</td>
</tr>
<tr>
<td>1/6"</td>
<td>3.00</td>
<td>2.40</td>
<td>1.80</td>
<td>4.32</td>
<td>-7.64</td>
<td>14.14</td>
</tr>
<tr>
<td>1/4"</td>
<td>4.00</td>
<td>3.20</td>
<td>2.40</td>
<td>7.68</td>
<td>-6.81</td>
<td>10.81</td>
</tr>
<tr>
<td>1/3.6" (<a href="//en.wikipedia.org/wiki/Nokia_Lumia_720" title="Nokia Lumia 720">Nokia Lumia 720</a>)<sup id="cite_ref-27" class="reference"><a href="#cite_note-27"><span>[</span>27<span>]</span></a></sup></td>
<td>5.00</td>
<td>4.00</td>
<td>3.00</td>
<td>12.0</td>
<td>-6.16</td>
<td>8.65</td>
</tr>
<tr>
<td>1/3.2" (<a href="//en.wikipedia.org/wiki/IPhone_5" title="IPhone 5">iPhone 5</a>)<sup id="cite_ref-28" class="reference"><a href="#cite_note-28"><span>[</span>28<span>]</span></a></sup></td>
<td>5.68</td>
<td>4.54</td>
<td>3.42</td>
<td>15.50</td>
<td>-5.80</td>
<td>7.61</td>
</tr>
<tr>
<td><i>Standard <a href="//en.wikipedia.org/wiki/8_mm_film#Standard_8" title="8 mm film">8mm film</a> frame</i></td>
<td>5.94</td>
<td>4.8</td>
<td>3.5</td>
<td>16.8</td>
<td>-5.73</td>
<td>7.28</td>
</tr>
<tr>
<td>1/3" (<a href="//en.wikipedia.org/wiki/IPhone_5S" title="IPhone 5S">iPhone 5S</a>)</td>
<td>6.00</td>
<td>4.80</td>
<td>3.60</td>
<td>17.30</td>
<td>-5.64</td>
<td>7.21</td>
</tr>
<tr>
<td>1/2.7"</td>
<td>6.72</td>
<td>5.37</td>
<td>4.04</td>
<td>21.70</td>
<td>-5.31</td>
<td>6.44</td>
</tr>
<tr>
<td><a href="//en.wikipedia.org/wiki/Super_8_film" title="Super 8 film"><i>Super 8mm film</i></a> <i>frame</i></td>
<td>7.04</td>
<td>5.79</td>
<td>4.01</td>
<td>23.22</td>
<td>-5.24</td>
<td>6.15</td>
</tr>
<tr>
<td>1/2.5" (Sony Cyber-shot DSC-T5)</td>
<td>7.18</td>
<td>5.76</td>
<td>4.29</td>
<td>24.70</td>
<td>-5.12</td>
<td>6.02</td>
</tr>
<tr>
<td>1/2.3" (<a href="//en.wikipedia.org/wiki/Pentax_Q" title="Pentax Q" class="mw-redirect">Pentax Q</a>) (Sony Cyber-shot DSC-W330)(gopro hero 3) (Panasonic HX-A500)</td>
<td>7.66</td>
<td>6.17</td>
<td>4.55</td>
<td>28.50</td>
<td>-4.92</td>
<td>5.64</td>
</tr>
<tr>
<td>1/2" (<a href="//en.wikipedia.org/w/index.php?title=Fujifilm_HS30EXR&amp;action=edit&amp;redlink=1" class="new" title="Fujifilm HS30EXR (page does not exist)">Fujifilm HS30EXR</a>)</td>
<td>8.00</td>
<td>6.40</td>
<td>4.80</td>
<td>30.70</td>
<td>-4.81</td>
<td>5.41</td>
</tr>
<tr>
<td>1/1.8" (<a href="//en.wikipedia.org/wiki/Nokia_N8" title="Nokia N8">Nokia N8</a>)</td>
<td>8.93</td>
<td>7.18</td>
<td>5.32</td>
<td>38.20</td>
<td>-4.50</td>
<td>4.84</td>
</tr>
<tr>
<td>1/1.7" (<a href="//en.wikipedia.org/wiki/Pentax_Q7" title="Pentax Q7" class="mw-redirect">Pentax Q7</a>)</td>
<td>9.50</td>
<td>7.60</td>
<td>5.70</td>
<td>43.30</td>
<td>-4.32</td>
<td>4.55</td>
</tr>
<tr>
<td>1/1.6"</td>
<td>10.07</td>
<td>8.08</td>
<td>6.01</td>
<td>48.56</td>
<td>-4.15</td>
<td>4.30</td>
</tr>
<tr>
<td>2/3" (<a href="//en.wikipedia.org/wiki/Lumia_1020" title="Lumia 1020" class="mw-redirect">Nokia Lumia 1020</a>, <a rel="nofollow" class="external text" href="http://www.imaging-resource.com/PRODS/XS1/XS1A.HTM">Fujifilm X-S1</a>, X20, XF1)</td>
<td>11.00</td>
<td>8.80</td>
<td>6.60</td>
<td>58.10</td>
<td>-3.89</td>
<td>3.93</td>
</tr>
<tr>
<td><i>Standard <a href="//en.wikipedia.org/wiki/16_mm_film#Standard_16_mm" title="16 mm film">16mm film</a> frame</i></td>
<td>12.7</td>
<td>10.26</td>
<td>7.49</td>
<td>76.85</td>
<td>-3.49</td>
<td>3.41</td>
</tr>
<tr>
<td>1/1.2" (<a href="//en.wikipedia.org/wiki/Nokia_808_PureView" title="Nokia 808 PureView">Nokia 808 PureView</a>)</td>
<td>13.33</td>
<td>10.67</td>
<td>8.00</td>
<td>85.33</td>
<td>-3.34</td>
<td>3.24</td>
</tr>
<tr>
<td>Blackmagic Pocket Cinema Camera</td>
<td>14.32</td>
<td>12.48</td>
<td>7.02</td>
<td>87.6</td>
<td>-3.30</td>
<td>3.02</td>
</tr>
<tr>
<td><i>Super 16mm film frame</i></td>
<td>14.54</td>
<td>12.52</td>
<td>7.41</td>
<td>92.80</td>
<td>-3.22</td>
<td>2.97</td>
</tr>
<tr>
<td>1" <a href="//en.wikipedia.org/wiki/Nikon_CX_format" title="Nikon CX format">Nikon CX</a>, <a href="//en.wikipedia.org/wiki/Sony_Cyber-shot_DSC-RX100" title="Sony Cyber-shot DSC-RX100">Sony Cyber-shot DSC-RX100</a> and <a href="//en.wikipedia.org/w/index.php?title=Sony_Cyber-shot_DSC-RX10&amp;action=edit&amp;redlink=1" class="new" title="Sony Cyber-shot DSC-RX10 (page does not exist)">Sony Cyber-shot DSC-RX10</a></td>
<td>15.86</td>
<td>13.20</td>
<td>8.80</td>
<td>116</td>
<td>-2.90</td>
<td>2.72</td>
</tr>
<tr>
<td>1" <a href="//en.wikipedia.org/wiki/Bolex#Digital_Bolex_D16" title="Bolex">Digital Bolex d16</a></td>
<td>16.00</td>
<td>12.80</td>
<td>9.60</td>
<td>123</td>
<td>-2.81</td>
<td>2.70</td>
</tr>
<tr>
<td>Blackmagic Cinema Camera EF</td>
<td>18.13</td>
<td>15.81</td>
<td>8.88</td>
<td>140</td>
<td>-2.62</td>
<td>2.38</td>
</tr>
<tr>
<td><a href="//en.wikipedia.org/wiki/Four_Thirds_system" title="Four Thirds system">Four Thirds</a>, <a href="//en.wikipedia.org/wiki/Micro_Four_Thirds_system" title="Micro Four Thirds system">Micro Four Thirds</a> ("4/3", "m4/3")</td>
<td>21.60</td>
<td>17.30</td>
<td>13</td>
<td>225</td>
<td>-1.94</td>
<td>2.00</td>
</tr>
<tr>
<td>1.5" <a href="//en.wikipedia.org/w/index.php?title=Canon_G1X&amp;action=edit&amp;redlink=1" class="new" title="Canon G1X (page does not exist)">Canon G1X</a></td>
<td>23.36</td>
<td>18.70</td>
<td>14.00</td>
<td>262</td>
<td>-1.72</td>
<td>1.85</td>
</tr>
<tr>
<td>Blackmagic Production Camera 4K</td>
<td>24.23</td>
<td>21.12</td>
<td>11.88</td>
<td>251</td>
<td>-1.78</td>
<td>1.79</td>
</tr>
<tr>
<td>original <a href="//en.wikipedia.org/wiki/Foveon_X3_sensor" title="Foveon X3 sensor">Sigma Foveon X3</a></td>
<td>24.90</td>
<td>20.70</td>
<td>13.80</td>
<td>286</td>
<td>-1.60</td>
<td>1.74</td>
</tr>
<tr>
<td>Canon <a href="//en.wikipedia.org/wiki/EF-S" title="EF-S" class="mw-redirect">EF-S</a>, <a href="//en.wikipedia.org/wiki/APS-C" title="APS-C">APS-C</a></td>
<td>26.70</td>
<td>22.20</td>
<td>14.80</td>
<td>329</td>
<td>-1.39</td>
<td>1.62</td>
</tr>
<tr>
<td><i>Standard <a href="//en.wikipedia.org/wiki/35_mm_film#Academy_format" title="35 mm film">35mm film</a> frame</i></td>
<td>27.20</td>
<td>22.0</td>
<td>16.0</td>
<td>352</td>
<td>-1.34</td>
<td>1.59</td>
</tr>
<tr>
<td><a href="//en.wikipedia.org/wiki/APS-C" title="APS-C">APS-C</a> (<a href="//en.wikipedia.org/wiki/Nikon_DX_format" title="Nikon DX format">Nikon DX</a>, <a href="//en.wikipedia.org/wiki/Pentax_K_mount" title="Pentax K mount" class="mw-redirect">Pentax K</a>, <a href="//en.wikipedia.org/wiki/Samsung_NX-mount" title="Samsung NX-mount">Samsung NX</a>, <a href="//en.wikipedia.org/wiki/Sony_A-mount" title="Sony A-mount" class="mw-redirect">Sony α DT</a>, <a href="//en.wikipedia.org/wiki/Sony_E-mount" title="Sony E-mount">Sony E</a>)</td>
<td>28.2–28.4</td>
<td>23.6–23.7</td>
<td>15.60</td>
<td>368–370</td>
<td>-1.23</td>
<td>1.52–1.54</td>
</tr>
<tr>
<td><i><a href="//en.wikipedia.org/wiki/Super_35" title="Super 35">Super 35mm</a> film frame</i></td>
<td>31.11</td>
<td>24.89</td>
<td>18.66</td>
<td>464</td>
<td>-0.95</td>
<td>1.39</td>
</tr>
<tr>
<td>Canon <a href="//en.wikipedia.org/wiki/APS-H" title="APS-H" class="mw-redirect">APS-H</a></td>
<td>33.50</td>
<td>27.90</td>
<td>18.60</td>
<td>519</td>
<td>-0.73</td>
<td>1.29</td>
</tr>
<tr>
<td><a href="//en.wikipedia.org/wiki/Full-frame_digital_SLR" title="Full-frame digital SLR">35mm full-frame</a>, (<a href="//en.wikipedia.org/wiki/Nikon" title="Nikon">Nikon FX</a>, <a href="//en.wikipedia.org/wiki/Sony_A-mount" title="Sony A-mount" class="mw-redirect">Sony α</a>, <a href="//en.wikipedia.org/wiki/Sony_FE-mount" title="Sony FE-mount" class="mw-redirect">Sony FE</a>, <a href="//en.wikipedia.org/wiki/Canon_(company)" title="Canon (company)" class="mw-redirect">Canon EF</a>)</td>
<td>43.2–43.3</td>
<td>36</td>
<td>23.9–24.3</td>
<td>860–864</td>
<td>0</td>
<td>1.0</td>
</tr>
<tr>
<td><a href="//en.wikipedia.org/wiki/Leica_Camera#S_.28medium_format_dSLR.29_series" title="Leica Camera">Leica S</a></td>
<td>54</td>
<td>45</td>
<td>30</td>
<td>1350</td>
<td>+0.64</td>
<td>0.80</td>
</tr>
<tr>
<td>Pentax <a href="//en.wikipedia.org/wiki/Pentax_645D" title="Pentax 645D" class="mw-redirect">645D</a></td>
<td>55</td>
<td>44</td>
<td>33</td>
<td>1452</td>
<td>+0.75</td>
<td>0.78</td>
</tr>
<tr>
<td><a href="//en.wikipedia.org/wiki/70_mm_film#Technical_specifications" title="70 mm film"><i>Standard 65mm</i></a> <i>film frame</i></td>
<td>57.30</td>
<td>52.48</td>
<td>23.01</td>
<td>1208</td>
<td>+0.81</td>
<td>0.76</td>
</tr>
<tr>
<td>Kodak KAF 39000 CCD<sup id="cite_ref-29" class="reference"><a href="#cite_note-29"><span>[</span>29<span>]</span></a></sup></td>
<td>61.30</td>
<td>49</td>
<td>36.80</td>
<td>1803</td>
<td>+1.06</td>
<td>0.71</td>
</tr>
<tr>
<td>Leaf AFi 10</td>
<td>66.57</td>
<td>56</td>
<td>36</td>
<td>2016</td>
<td>+1.22</td>
<td>0.65</td>
</tr>
<tr>
<td><a href="//en.wikipedia.org/wiki/Medium_format_(film)" title="Medium format (film)">Medium-format</a> (<a href="//en.wikipedia.org/wiki/Hasselblad" title="Hasselblad">Hasselblad</a> H5D-60)<sup id="cite_ref-30" class="reference"><a href="#cite_note-30"><span>[</span>30<span>]</span></a></sup></td>
<td>67.08</td>
<td>53.7</td>
<td>40.2</td>
<td>2159</td>
<td>+1.26</td>
<td>0.65</td>
</tr>
<tr>
<td>Phase One <a href="//en.wikipedia.org/wiki/Phase_One_(company)" title="Phase One (company)">P 65+</a>, IQ160, IQ180</td>
<td>67.40</td>
<td>53.90</td>
<td>40.40</td>
<td>2178</td>
<td>+1.33</td>
<td>0.64</td>
</tr>
<tr>
<td><a href="//en.wikipedia.org/wiki/70_mm_film#IMAX_.2815.2F70.29" title="70 mm film"><i>IMAX</i></a> <i>film frame</i></td>
<td>87.91</td>
<td>70.41</td>
<td>52.63</td>
<td>3706</td>
<td>+2.05</td>
<td>0.49</td>
</tr>
</table>
<h2><span class="mw-headline" id="See_also">See also</span></h2>
<ul>
<li><a href="//en.wikipedia.org/wiki/Full-frame_digital_SLR" title="Full-frame digital SLR">Full-frame digital SLR</a></li>
<li><a href="//en.wikipedia.org/wiki/Digital_photography#Sensor_size_and_angle_of_view" title="Digital photography">Sensor size and angle of view</a></li>
<li><a href="//en.wikipedia.org/wiki/35_mm_equivalent_focal_length" title="35 mm equivalent focal length">35 mm equivalent focal length</a></li>
<li><a href="//en.wikipedia.org/wiki/Film_format" title="Film format">Film format</a></li>
<li><a href="//en.wikipedia.org/wiki/Digital_versus_film_photography" title="Digital versus film photography">Digital versus film photography</a></li>
<li><a href="//en.wikipedia.org/wiki/List_of_large_sensor_interchangeable-lens_video_cameras" title="List of large sensor interchangeable-lens video cameras">List of large sensor interchangeable-lens video cameras</a></li>
</ul>
<h2><span class="mw-headline" id="Notes_and_references">Notes and references</span></h2>
<div class="reflist" style="list-style-type: decimal;">
<ol class="references">
<li id="cite_note-noise-1"><span class="mw-cite-backlink">^ <a href="#cite_ref-noise_1-0"><sup><i><b>a</b></i></sup></a> <a href="#cite_ref-noise_1-1"><sup><i><b>b</b></i></sup></a></span> <span class="reference-text"><span class="citation web">Fellers, Thomas J.; Davidson, Michael W. <a rel="nofollow" class="external text" href="http://micro.magnet.fsu.edu/primer/digitalimaging/concepts/ccdsnr.html">"CCD Noise Sources and Signal-to-Noise Ratio"</a>. Hamamatsu Corporation<span class="reference-accessdate">. Retrieved 20 November 2013</span>.</span><span title="ctx_ver=Z39.88-2004&amp;rfr_id=info%3Asid%2Fen.wikipedia.org%3AImage+sensor+format&amp;rft.au=Davidson%2C+Michael+W.&amp;rft.au=Fellers%2C+Thomas+J.&amp;rft.aufirst=Thomas+J.&amp;rft.aulast=Fellers&amp;rft.btitle=CCD+Noise+Sources+and+Signal-to-Noise+Ratio&amp;rft.genre=book&amp;rft_id=http%3A%2F%2Fmicro.magnet.fsu.edu%2Fprimer%2Fdigitalimaging%2Fconcepts%2Fccdsnr.html&amp;rft.pub=Hamamatsu+Corporation&amp;rft_val_fmt=info%3Aofi%2Ffmt%3Akev%3Amtx%3Abook" class="Z3988"><span style="display:none;">&#160;</span></span></span></li>
<li id="cite_note-2"><span class="mw-cite-backlink"><b><a href="#cite_ref-2">^</a></b></span> <span class="reference-text"><span class="citation web">Aptina Imaging Corporation. <a rel="nofollow" class="external text" href="http://www.aptina.com/products/technology/DR-Pix_WhitePaper.pdf">"Leveraging Dynamic Response Pixel Technology to Optimize Inter-scene Dynamic Range"</a>. Aptina Imaging Corporation<span class="reference-accessdate">. Retrieved 17 December 2011</span>.</span><span title="ctx_ver=Z39.88-2004&amp;rfr_id=info%3Asid%2Fen.wikipedia.org%3AImage+sensor+format&amp;rft.au=Aptina+Imaging+Corporation&amp;rft.aulast=Aptina+Imaging+Corporation&amp;rft.btitle=Leveraging+Dynamic+Response+Pixel+Technology+to+Optimize+Inter-scene+Dynamic+Range&amp;rft.genre=book&amp;rft_id=http%3A%2F%2Fwww.aptina.com%2Fproducts%2Ftechnology%2FDR-Pix_WhitePaper.pdf&amp;rft.pub=Aptina+Imaging+Corporation&amp;rft_val_fmt=info%3Aofi%2Ffmt%3Akev%3Amtx%3Abook" class="Z3988"><span style="display:none;">&#160;</span></span></span></li>
<li id="cite_note-3"><span class="mw-cite-backlink"><b><a href="#cite_ref-3">^</a></b></span> <span class="reference-text"><span class="citation journal">Loukianova, Natalia V.; Folkerts, Hein Otto; Maas, Joris P. V.; Verbugt, Joris P. V.; Daniël W. E. Mierop, Adri J.; Hoekstra, Willem; Roks, Edwin and Theuwissen, Albert J. P. (January 2003). <a rel="nofollow" class="external text" href="http://www.harvestimaging.com/pubdocs/073_2003_jan_TED_leakage_current.pdf">"Leakage Current Modeling of Test Structures for Characterization of Dark Current in CMOS Image Sensors"</a>. <i>IEEE Transactions on Electron Devices</i> <b>50</b> (1): 77–83. <a href="//en.wikipedia.org/wiki/Digital_object_identifier" title="Digital object identifier">doi</a>:<a rel="nofollow" class="external text" href="http://dx.doi.org/10.1109%2FTED.2002.807249">10.1109/TED.2002.807249</a><span class="reference-accessdate">. Retrieved 17 December 2011</span>.</span><span title="ctx_ver=Z39.88-2004&amp;rfr_id=info%3Asid%2Fen.wikipedia.org%3AImage+sensor+format&amp;rft.atitle=Leakage+Current+Modeling+of+Test+Structures+for+Characterization+of+Dark+Current+in+CMOS+Image+Sensors&amp;rft.au=Dani%C3%ABl+W.+E.++Mierop%2C+Adri+J.&amp;rft.aufirst=Natalia+V.&amp;rft.au=Folkerts%2C+Hein+Otto&amp;rft.au=Hoekstra%2C+Willem&amp;rft.aulast=Loukianova&amp;rft.au=Loukianova%2C+Natalia+V.&amp;rft.au=Maas%2C+Joris+P.+V.&amp;rft.au=Roks%2C+Edwin+and+Theuwissen%2C+Albert+J.+P.&amp;rft.au=Verbugt%2C+Joris+P.+V.&amp;rft.date=January+2003&amp;rft.genre=article&amp;rft_id=http%3A%2F%2Fwww.harvestimaging.com%2Fpubdocs%2F073_2003_jan_TED_leakage_current.pdf&amp;rft_id=info%3Adoi%2F10.1109%2FTED.2002.807249&amp;rft.issue=1&amp;rft.jtitle=IEEE+Transactions+on+Electron+Devices&amp;rft.pages=77-83&amp;rft_val_fmt=info%3Aofi%2Ffmt%3Akev%3Amtx%3Ajournal&amp;rft.volume=50" class="Z3988"><span style="display:none;">&#160;</span></span></span></li>
<li id="cite_note-4"><span class="mw-cite-backlink"><b><a href="#cite_ref-4">^</a></b></span> <span class="reference-text"><span class="citation web"><a rel="nofollow" class="external text" href="http://www.ccd.com/ccd109.html">"Dark Count"</a>. Apogee Imaging Systems<span class="reference-accessdate">. Retrieved 17 December 2011</span>.</span><span title="ctx_ver=Z39.88-2004&amp;rfr_id=info%3Asid%2Fen.wikipedia.org%3AImage+sensor+format&amp;rft.btitle=Dark+Count&amp;rft.genre=book&amp;rft_id=http%3A%2F%2Fwww.ccd.com%2Fccd109.html&amp;rft.pub=Apogee+Imaging+Systems&amp;rft_val_fmt=info%3Aofi%2Ffmt%3Akev%3Amtx%3Abook" class="Z3988"><span style="display:none;">&#160;</span></span></span></li>
<li id="cite_note-5"><span class="mw-cite-backlink"><b><a href="#cite_ref-5">^</a></b></span> <span class="reference-text"><span class="citation journal">Kavusi, Sam; El Gamal, Abbas (2004). <a rel="nofollow" class="external text" href="http://www-isl.stanford.edu/groups/elgamal/abbas_publications/C099.pdf">"Quantitative Study of High Dynamic Range Image Sensor Architectures"</a>. <i>Proc. of SPIE-IS&amp;T Electronic Imaging</i> <b>5301</b>: 264–275<span class="reference-accessdate">. Retrieved 17 December 2011</span>.</span><span title="ctx_ver=Z39.88-2004&amp;rfr_id=info%3Asid%2Fen.wikipedia.org%3AImage+sensor+format&amp;rft.atitle=Quantitative+Study+of+High+Dynamic+Range+Image+Sensor+Architectures&amp;rft.au=El+Gamal%2C+Abbas&amp;rft.aufirst=Sam&amp;rft.au=Kavusi%2C+Sam&amp;rft.aulast=Kavusi&amp;rft.date=2004&amp;rft.genre=article&amp;rft_id=http%3A%2F%2Fwww-isl.stanford.edu%2Fgroups%2Felgamal%2Fabbas_publications%2FC099.pdf&amp;rft.jtitle=Proc.+of+SPIE-IS%26T+Electronic+Imaging&amp;rft.pages=264-275&amp;rft_val_fmt=info%3Aofi%2Ffmt%3Akev%3Amtx%3Ajournal&amp;rft.volume=5301" class="Z3988"><span style="display:none;">&#160;</span></span></span></li>
<li id="cite_note-LLResolution-6"><span class="mw-cite-backlink"><b><a href="#cite_ref-LLResolution_6-0">^</a></b></span> <span class="reference-text"><span class="citation web">Osuna, Rubén &amp; García, Efraín. <a rel="nofollow" class="external text" href="http://www.luminous-landscape.com/tutorials/resolution.shtml">"Do Sensors "Outresolve" Lenses?"</a>. The Luminous Landscape<span class="reference-accessdate">. Retrieved 21 December 2011</span>.</span><span title="ctx_ver=Z39.88-2004&amp;rfr_id=info%3Asid%2Fen.wikipedia.org%3AImage+sensor+format&amp;rft.aulast=Osuna%2C+Rub%C3%A9n+%26+Garc%C3%ADa%2C+Efra%C3%ADn&amp;rft.au=Osuna%2C+Rub%C3%A9n+%26+Garc%C3%ADa%2C+Efra%C3%ADn&amp;rft.btitle=Do+Sensors+%22Outresolve%22+Lenses%3F&amp;rft.genre=book&amp;rft_id=http%3A%2F%2Fwww.luminous-landscape.com%2Ftutorials%2Fresolution.shtml&amp;rft.pub=The+Luminous+Landscape&amp;rft_val_fmt=info%3Aofi%2Ffmt%3Akev%3Amtx%3Abook" class="Z3988"><span style="display:none;">&#160;</span></span></span></li>
<li id="cite_note-DiffractionMTF-7"><span class="mw-cite-backlink"><b><a href="#cite_ref-DiffractionMTF_7-0">^</a></b></span> <span class="reference-text"><span class="citation book">Boreman, Glenn D. (2001). <a rel="nofollow" class="external text" href="http://spie.org/x34304.xml"><i>Modulation Transfer Function in Optical and Electro-Optical Systems</i></a>. SPIE Press. p.&#160;120. <a href="//en.wikipedia.org/wiki/International_Standard_Book_Number" title="International Standard Book Number">ISBN</a>&#160;<a href="//en.wikipedia.org/wiki/Special:BookSources/978-0-8194-4143-0" title="Special:BookSources/978-0-8194-4143-0">978-0-8194-4143-0</a>.</span><span title="ctx_ver=Z39.88-2004&amp;rfr_id=info%3Asid%2Fen.wikipedia.org%3AImage+sensor+format&amp;rft.au=Boreman%2C+Glenn+D.&amp;rft.aufirst=Glenn+D.&amp;rft.aulast=Boreman&amp;rft.btitle=Modulation+Transfer+Function+in+Optical+and+Electro-Optical+Systems&amp;rft.date=2001&amp;rft.genre=book&amp;rft_id=http%3A%2F%2Fspie.org%2Fx34304.xml&amp;rft.isbn=978-0-8194-4143-0&amp;rft.pages=120&amp;rft.pub=SPIE+Press&amp;rft_val_fmt=info%3Aofi%2Ffmt%3Akev%3Amtx%3Abook" class="Z3988"><span style="display:none;">&#160;</span></span></span></li>
<li id="cite_note-8"><span class="mw-cite-backlink"><b><a href="#cite_ref-8">^</a></b></span> <span class="reference-text"><span class="citation journal">Ozaktas, Haldun M; Urey, Hakan; Lohmann, Adolf W. (1994). "Scaling of diffractive and refractive lenses for optical computing and interconnections". <i>Applied Optics</i> <b>33</b> (17): 3782–3789. <a href="//en.wikipedia.org/wiki/Digital_object_identifier" title="Digital object identifier">doi</a>:<a rel="nofollow" class="external text" href="http://dx.doi.org/10.1364%2FAO.33.003782">10.1364/AO.33.003782</a>.</span><span title="ctx_ver=Z39.88-2004&amp;rfr_id=info%3Asid%2Fen.wikipedia.org%3AImage+sensor+format&amp;rft.atitle=Scaling+of+diffractive+and+refractive+lenses+for+optical+computing+and+interconnections&amp;rft.aufirst=Haldun+M&amp;rft.aulast=Ozaktas&amp;rft.au=Lohmann%2C+Adolf+W.&amp;rft.au=Ozaktas%2C+Haldun+M&amp;rft.au=Urey%2C+Hakan&amp;rft.date=1994&amp;rft.genre=article&amp;rft_id=info%3Adoi%2F10.1364%2FAO.33.003782&amp;rft.issue=17&amp;rft.jtitle=Applied+Optics&amp;rft.pages=3782-3789&amp;rft_val_fmt=info%3Aofi%2Ffmt%3Akev%3Amtx%3Ajournal&amp;rft.volume=33" class="Z3988"><span style="display:none;">&#160;</span></span></span></li>
<li id="cite_note-9"><span class="mw-cite-backlink"><b><a href="#cite_ref-9">^</a></b></span> <span class="reference-text"><span class="citation book">Goodman, Joseph W (2005). <i>Introduction to Fourier optics, 3rd edition</i>. Greenwood Village, CO: Roberts and Company. p.&#160;26. <a href="//en.wikipedia.org/wiki/International_Standard_Book_Number" title="International Standard Book Number">ISBN</a>&#160;<a href="//en.wikipedia.org/wiki/Special:BookSources/0-9747077-2-4" title="Special:BookSources/0-9747077-2-4">0-9747077-2-4</a>.</span><span title="ctx_ver=Z39.88-2004&amp;rfr_id=info%3Asid%2Fen.wikipedia.org%3AImage+sensor+format&amp;rft.aufirst=Joseph+W&amp;rft.au=Goodman%2C+Joseph+W&amp;rft.aulast=Goodman&amp;rft.btitle=Introduction+to+Fourier+optics%2C+3rd+edition&amp;rft.date=2005&amp;rft.genre=book&amp;rft.isbn=0-9747077-2-4&amp;rft.pages=26&amp;rft.place=Greenwood+Village%2C+CO&amp;rft.pub=Roberts+and+Company&amp;rft_val_fmt=info%3Aofi%2Ffmt%3Akev%3Amtx%3Abook" class="Z3988"><span style="display:none;">&#160;</span></span></span></li>
<li id="cite_note-10"><span class="mw-cite-backlink"><b><a href="#cite_ref-10">^</a></b></span> <span class="reference-text"><span class="citation web">Nasse, H. H. <a rel="nofollow" class="external text" href="http://www.zeiss.com/C12578620052CA69/0/58D501E36518AFC9C12578D2004104E1/$file/cln_39_en_tessar.pdf">"From the Series of Articles on Lens Names: Tessar"</a>. Carl Zeiss AG<span class="reference-accessdate">. Retrieved 19 December 2011</span>.</span><span title="ctx_ver=Z39.88-2004&amp;rfr_id=info%3Asid%2Fen.wikipedia.org%3AImage+sensor+format&amp;rft.aufirst=H.+H.&amp;rft.aulast=Nasse&amp;rft.au=Nasse%2C+H.+H.&amp;rft.btitle=From+the+Series+of+Articles+on+Lens+Names%3A+Tessar&amp;rft.genre=book&amp;rft_id=http%3A%2F%2Fwww.zeiss.com%2FC12578620052CA69%2F0%2F58D501E36518AFC9C12578D2004104E1%2F%24file%2Fcln_39_en_tessar.pdf&amp;rft.pub=Carl+Zeiss+AG.&amp;rft_val_fmt=info%3Aofi%2Ffmt%3Akev%3Amtx%3Abook" class="Z3988"><span style="display:none;">&#160;</span></span></span></li>
<li id="cite_note-11"><span class="mw-cite-backlink"><b><a href="#cite_ref-11">^</a></b></span> <span class="reference-text"><span class="citation web">Simon Crisp. <a rel="nofollow" class="external text" href="http://www.gizmag.com/camera-sensor-size-guide/26684/">"Camera sensor size: Why does it matter and exactly how big are they?"</a><span class="reference-accessdate">. Retrieved January 29, 2014</span>.</span><span title="ctx_ver=Z39.88-2004&amp;rfr_id=info%3Asid%2Fen.wikipedia.org%3AImage+sensor+format&amp;rft.aulast=Simon+Crisp&amp;rft.au=Simon+Crisp&amp;rft.btitle=Camera+sensor+size%3A+Why+does+it+matter+and+exactly+how+big+are+they%3F&amp;rft.genre=book&amp;rft_id=http%3A%2F%2Fwww.gizmag.com%2Fcamera-sensor-size-guide%2F26684%2F&amp;rft_val_fmt=info%3Aofi%2Ffmt%3Akev%3Amtx%3Abook" class="Z3988"><span style="display:none;">&#160;</span></span></span></li>
<li id="cite_note-Catrysse-12"><span class="mw-cite-backlink"><b><a href="#cite_ref-Catrysse_12-0">^</a></b></span> <span class="reference-text"><span class="citation journal">Catrysse, Peter B.; Wandell, Brian A. (2005). <a rel="nofollow" class="external text" href="http://www.imageval.com/public/Papers/EI%205678-01%20Peter%20Catrysse.pdf">"Roadmap for CMOS image sensors: Moore meets Planck and Sommerfeld"</a>. <i>Proceedings of the International Society for Optical Engineering</i> <b>5678</b> (1). <a href="//en.wikipedia.org/wiki/Digital_object_identifier" title="Digital object identifier">doi</a>:<a rel="nofollow" class="external text" href="http://dx.doi.org/10.1117%2F12.592483">10.1117/12.592483</a><span class="reference-accessdate">. Retrieved 29 January 2012</span>.</span><span title="ctx_ver=Z39.88-2004&amp;rfr_id=info%3Asid%2Fen.wikipedia.org%3AImage+sensor+format&amp;rft.atitle=Roadmap+for+CMOS+image+sensors%3A+Moore+meets+Planck+and+Sommerfeld&amp;rft.au=Catrysse%2C+Peter+B.&amp;rft.aufirst=Peter+B.&amp;rft.aulast=Catrysse&amp;rft.au=Wandell%2C+Brian+A.&amp;rft.date=2005&amp;rft.genre=article&amp;rft_id=http%3A%2F%2Fwww.imageval.com%2Fpublic%2FPapers%2FEI%25205678-01%2520Peter%2520Catrysse.pdf&amp;rft_id=info%3Adoi%2F10.1117%2F12.592483&amp;rft.issue=1&amp;rft.jtitle=Proceedings+of+the+International+Society+for+Optical+Engineering&amp;rft_val_fmt=info%3Aofi%2Ffmt%3Akev%3Amtx%3Ajournal&amp;rft.volume=5678" class="Z3988"><span style="display:none;">&#160;</span></span></span></li>
<li id="cite_note-13"><span class="mw-cite-backlink"><b><a href="#cite_ref-13">^</a></b></span> <span class="reference-text"><span class="citation web">DxOmark. <a rel="nofollow" class="external text" href="http://www.dxomark.com/index.php/Publications/DxOMark-Insights/F-stop-blues">"F-stop blues"</a>. <i>DxOMark Insights</i><span class="reference-accessdate">. Retrieved 29 January 2012</span>.</span><span title="ctx_ver=Z39.88-2004&amp;rfr_id=info%3Asid%2Fen.wikipedia.org%3AImage+sensor+format&amp;rft.atitle=F-stop+blues&amp;rft.au=DxOmark&amp;rft.aulast=DxOmark&amp;rft.genre=article&amp;rft_id=http%3A%2F%2Fwww.dxomark.com%2Findex.php%2FPublications%2FDxOMark-Insights%2FF-stop-blues&amp;rft.jtitle=DxOMark+Insights&amp;rft_val_fmt=info%3Aofi%2Ffmt%3Akev%3Amtx%3Ajournal" class="Z3988"><span style="display:none;">&#160;</span></span></span></li>
<li id="cite_note-14"><span class="mw-cite-backlink"><b><a href="#cite_ref-14">^</a></b></span> <span class="reference-text"><span class="citation web">Aptina Imaging Corporation. <a rel="nofollow" class="external text" href="http://www.aptina.com/news/FSI-BSI-WhitePaper.pdf">"An Objective Look at FSI and BSI"</a>. <i>Aptina Technology White Paper</i><span class="reference-accessdate">. Retrieved 29 January 2012</span>.</span><span title="ctx_ver=Z39.88-2004&amp;rfr_id=info%3Asid%2Fen.wikipedia.org%3AImage+sensor+format&amp;rft.atitle=An+Objective+Look+at+FSI+and+BSI&amp;rft.au=Aptina+Imaging+Corporation&amp;rft.aulast=Aptina+Imaging+Corporation&amp;rft.genre=article&amp;rft_id=http%3A%2F%2Fwww.aptina.com%2Fnews%2FFSI-BSI-WhitePaper.pdf&amp;rft.jtitle=Aptina+Technology+White+Paper&amp;rft_val_fmt=info%3Aofi%2Ffmt%3Akev%3Amtx%3Ajournal" class="Z3988"><span style="display:none;">&#160;</span></span></span></li>
<li id="cite_note-15"><span class="mw-cite-backlink"><b><a href="#cite_ref-15">^</a></b></span> <span class="reference-text"><span class="citation pressrelease"><a rel="nofollow" class="external text" href="http://www.dpreview.net/news/article_print.asp?date=0510&amp;article=05102101kodak_3936mpccd">"Kodak Announces Highest Resolution Image Sensors for Professional Photography"</a> (Press release). Kodak. 2005-10-20.</span><span title="ctx_ver=Z39.88-2004&amp;rfr_id=info%3Asid%2Fen.wikipedia.org%3AImage+sensor+format&amp;rft.btitle=Kodak+Announces+Highest+Resolution+Image+Sensors+for+Professional+Photography&amp;rft.date=2005-10-20&amp;rft.genre=book&amp;rft_id=http%3A%2F%2Fwww.dpreview.net%2Fnews%2Farticle_print.asp%3Fdate%3D0510%26article%3D05102101kodak_3936mpccd&amp;rft.pub=Kodak&amp;rft_val_fmt=info%3Aofi%2Ffmt%3Akev%3Amtx%3Abook" class="Z3988"><span style="display:none;">&#160;</span></span><sup class="noprint Inline-Template"><span style="white-space: nowrap;">[<i><a href="//en.wikipedia.org/wiki/Wikipedia:Link_rot" title="Wikipedia:Link rot"><span title="&#160;since March 2013">dead link</span></a></i>]</span></sup></span></li>
<li id="cite_note-16"><span class="mw-cite-backlink"><b><a href="#cite_ref-16">^</a></b></span> <span class="reference-text"><span class="citation web"><a rel="nofollow" class="external text" href="http://www.phaseone.com/en/Digital-Backs/P65/~/media/Phase%20One/Products/Documents/Phase-One-digital-back-overview.ashx">"The Phase One P+ Product Range"</a>. PHASE ONE<span class="reference-accessdate">. Retrieved 2010-06-07</span>.</span><span title="ctx_ver=Z39.88-2004&amp;rfr_id=info%3Asid%2Fen.wikipedia.org%3AImage+sensor+format&amp;rft.btitle=The+Phase+One+P%2B+Product+Range&amp;rft.genre=book&amp;rft_id=http%3A%2F%2Fwww.phaseone.com%2Fen%2FDigital-Backs%2FP65%2F~%2Fmedia%2FPhase%2520One%2FProducts%2FDocuments%2FPhase-One-digital-back-overview.ashx&amp;rft.pub=PHASE+ONE&amp;rft_val_fmt=info%3Aofi%2Ffmt%3Akev%3Amtx%3Abook" class="Z3988"><span style="display:none;">&#160;</span></span></span></li>
<li id="cite_note-17"><span class="mw-cite-backlink"><b><a href="#cite_ref-17">^</a></b></span> <span class="reference-text"><span class="citation pressrelease"><a rel="nofollow" class="external text" href="http://www.dpreview.com/news/0809/08092301_leica_s2.asp">"Leica S2 with 56% larger sensor than full frame"</a> (Press release). Leica. 2008-09-23<span class="reference-accessdate">. Retrieved 2010-06-07</span>.</span><span title="ctx_ver=Z39.88-2004&amp;rfr_id=info%3Asid%2Fen.wikipedia.org%3AImage+sensor+format&amp;rft.btitle=Leica+S2+with+56%25+larger+sensor+than+full+frame&amp;rft.date=2008-09-23&amp;rft.genre=book&amp;rft_id=http%3A%2F%2Fwww.dpreview.com%2Fnews%2F0809%2F08092301_leica_s2.asp&amp;rft.pub=Leica&amp;rft_val_fmt=info%3Aofi%2Ffmt%3Akev%3Amtx%3Abook" class="Z3988"><span style="display:none;">&#160;</span></span></span></li>
<li id="cite_note-18"><span class="mw-cite-backlink"><b><a href="#cite_ref-18">^</a></b></span> <span class="reference-text"><span class="citation pressrelease"><a rel="nofollow" class="external text" href="http://www.dpreview.com/news/1003/10031002pentax645d.asp">"Pentax unveils 40MP 645D medium format DSLR"</a> (Press release). Pentax. 2010-03-10<span class="reference-accessdate">. Retrieved 2010-12-21</span>.</span><span title="ctx_ver=Z39.88-2004&amp;rfr_id=info%3Asid%2Fen.wikipedia.org%3AImage+sensor+format&amp;rft.btitle=Pentax+unveils+40MP+645D+medium+format+DSLR&amp;rft.date=2010-03-10&amp;rft.genre=book&amp;rft_id=http%3A%2F%2Fwww.dpreview.com%2Fnews%2F1003%2F10031002pentax645d.asp&amp;rft.pub=Pentax&amp;rft_val_fmt=info%3Aofi%2Ffmt%3Akev%3Amtx%3Abook" class="Z3988"><span style="display:none;">&#160;</span></span></span></li>
<li id="cite_note-19"><span class="mw-cite-backlink"><b><a href="#cite_ref-19">^</a></b></span> <span class="reference-text"><a rel="nofollow" class="external text" href="http://www.dpreview.com/news/1109/11092119nikonJ1.asp#press">Nikon unveils J1 small sensor mirrorless camera as part of Nikon 1 system</a> at dpreview.com</span></li>
<li id="cite_note-canon-wp-20"><span class="mw-cite-backlink"><b><a href="#cite_ref-canon-wp_20-0">^</a></b></span> <span class="reference-text"><span class="citation pressrelease"><a rel="nofollow" class="external text" href="http://www.robgalbraith.com/public_files/Canon_Full-Frame_CMOS_White_Paper.pdf">"Canon's Full Frame CMOS Sensors"</a> (Press release). 2006<span class="reference-accessdate">. Retrieved 2013-05-02</span>.</span><span title="ctx_ver=Z39.88-2004&amp;rfr_id=info%3Asid%2Fen.wikipedia.org%3AImage+sensor+format&amp;rft.btitle=Canon%27s+Full+Frame+CMOS+Sensors&amp;rft.date=2006&amp;rft.genre=book&amp;rft_id=http%3A%2F%2Fwww.robgalbraith.com%2Fpublic_files%2FCanon_Full-Frame_CMOS_White_Paper.pdf&amp;rft_val_fmt=info%3Aofi%2Ffmt%3Akev%3Amtx%3Abook" class="Z3988"><span style="display:none;">&#160;</span></span></span></li>
<li id="cite_note-21"><span class="mw-cite-backlink"><b><a href="#cite_ref-21">^</a></b></span> <span class="reference-text"><a rel="nofollow" class="external free" href="http://europe.nokia.com/PRODUCT_METADATA_0/Products/Phones/8000-series/808/Nokia808PureView_Whitepaper.pdf">http://europe.nokia.com/PRODUCT_METADATA_0/Products/Phones/8000-series/808/Nokia808PureView_Whitepaper.pdf</a> Nokia PureView imaging technology whitepaper</span></li>
<li id="cite_note-22"><span class="mw-cite-backlink"><b><a href="#cite_ref-22">^</a></b></span> <span class="reference-text"><span class="citation news">Pogue, David (2010-12-22). <a rel="nofollow" class="external text" href="http://www.nytimes.com/2010/12/23/technology/personaltech/23pogue.html?ref=technology">"Small Cameras With Big Sensors, and How to Compare Them"</a>. <i>The New York Times</i>.</span><span title="ctx_ver=Z39.88-2004&amp;rfr_id=info%3Asid%2Fen.wikipedia.org%3AImage+sensor+format&amp;rft.atitle=Small+Cameras+With+Big+Sensors%2C+and+How+to+Compare+Them&amp;rft.aufirst=David&amp;rft.aulast=Pogue&amp;rft.au=Pogue%2C+David&amp;rft.date=2010-12-22&amp;rft.genre=article&amp;rft_id=http%3A%2F%2Fwww.nytimes.com%2F2010%2F12%2F23%2Ftechnology%2Fpersonaltech%2F23pogue.html%3Fref%3Dtechnology&amp;rft.jtitle=The+New+York+Times&amp;rft_val_fmt=info%3Aofi%2Ffmt%3Akev%3Amtx%3Ajournal" class="Z3988"><span style="display:none;">&#160;</span></span></span></li>
<li id="cite_note-dpreview-sensor-sizes-23"><span class="mw-cite-backlink">^ <a href="#cite_ref-dpreview-sensor-sizes_23-0"><sup><i><b>a</b></i></sup></a> <a href="#cite_ref-dpreview-sensor-sizes_23-1"><sup><i><b>b</b></i></sup></a></span> <span class="reference-text"><span class="citation web">Bockaert, Vincent. <a rel="nofollow" class="external text" href="http://www.dpreview.com/learn/?/Glossary/Camera_System/sensor_sizes_01.htm">"Sensor Sizes: Camera System: Glossary: Learn"</a>. <a href="//en.wikipedia.org/wiki/Digital_Photography_Review" title="Digital Photography Review">Digital Photography Review</a><span class="reference-accessdate">. Retrieved 2012-04-09</span>.</span><span title="ctx_ver=Z39.88-2004&amp;rfr_id=info%3Asid%2Fen.wikipedia.org%3AImage+sensor+format&amp;rft.au=Bockaert%2C+Vincent&amp;rft.aufirst=Vincent&amp;rft.aulast=Bockaert&amp;rft.btitle=Sensor+Sizes%3A+Camera+System%3A+Glossary%3A+Learn&amp;rft.genre=book&amp;rft_id=http%3A%2F%2Fwww.dpreview.com%2Flearn%2F%3F%2FGlossary%2FCamera_System%2Fsensor_sizes_01.htm&amp;rft.pub=Digital+Photography+Review&amp;rft_val_fmt=info%3Aofi%2Ffmt%3Akev%3Amtx%3Abook" class="Z3988"><span style="display:none;">&#160;</span></span></span></li>
<li id="cite_note-dxoa-24"><span class="mw-cite-backlink"><b><a href="#cite_ref-dxoa_24-0">^</a></b></span> <span class="reference-text"><a rel="nofollow" class="external text" href="http://www.dxomark.com/index.php/Cameras/Camera-Sensor-Ratings">Camera Sensor Ratings</a> DxOMark</span></li>
<li id="cite_note-imac-25"><span class="mw-cite-backlink"><b><a href="#cite_ref-imac_25-0">^</a></b></span> <span class="reference-text"><a rel="nofollow" class="external text" href="http://www.imaging-resource.com/IMCOMP/COMPS01.HTM">Imaging-resource: Sample images Comparometer</a> Imaging-resource</span></li>
<li id="cite_note-26"><span class="mw-cite-backlink"><b><a href="#cite_ref-26">^</a></b></span> <span class="reference-text">Defined here as the ratio of the diagonal of a full 35&#160;frame to that of the sensor format, that is CF=diag<sub>35mm</sub> / diag<sub>sensor</sub>.</span></li>
<li id="cite_note-27"><span class="mw-cite-backlink"><b><a href="#cite_ref-27">^</a></b></span> <span class="reference-text"><span id="CITEREF2013" class="citation"><a rel="nofollow" class="external text" href="http://www.gsmarena.com/nokia_lumia_720-5321.php"><i>Nokia Lumia 720 – Full phone specifications</i></a>, GSMArena.com, February 25, 2013<span class="reference-accessdate">, retrieved 2013-09-21</span></span><span title="ctx_ver=Z39.88-2004&amp;rfr_id=info%3Asid%2Fen.wikipedia.org%3AImage+sensor+format&amp;rft.btitle=Nokia+Lumia+720+%E2%80%93+Full+phone+specifications&amp;rft.date=February+25%2C+2013&amp;rft.genre=book&amp;rft_id=http%3A%2F%2Fwww.gsmarena.com%2Fnokia_lumia_720-5321.php&amp;rft.pub=GSMArena.com&amp;rft_val_fmt=info%3Aofi%2Ffmt%3Akev%3Amtx%3Abook" class="Z3988"><span style="display:none;">&#160;</span></span></span></li>
<li id="cite_note-28"><span class="mw-cite-backlink"><b><a href="#cite_ref-28">^</a></b></span> <span class="reference-text"><span id="CITEREF2013" class="citation"><a rel="nofollow" class="external text" href="http://www.gizmag.com/camera-sensor-size-guide/26684/"><i>Camera sensor size: Why does it matter and exactly how big are they?</i></a>, Gizmag, March 21, 2013<span class="reference-accessdate">, retrieved 2013-06-19</span></span><span title="ctx_ver=Z39.88-2004&amp;rfr_id=info%3Asid%2Fen.wikipedia.org%3AImage+sensor+format&amp;rft.btitle=Camera+sensor+size%3A+Why+does+it+matter+and+exactly+how+big+are+they%3F&amp;rft.date=March+21%2C+2013&amp;rft.genre=book&amp;rft_id=http%3A%2F%2Fwww.gizmag.com%2Fcamera-sensor-size-guide%2F26684%2F&amp;rft.pub=Gizmag&amp;rft_val_fmt=info%3Aofi%2Ffmt%3Akev%3Amtx%3Abook" class="Z3988"><span style="display:none;">&#160;</span></span></span></li>
<li id="cite_note-29"><span class="mw-cite-backlink"><b><a href="#cite_ref-29">^</a></b></span> <span class="reference-text"><span id="CITEREF2010" class="citation"><a rel="nofollow" class="external text" href="http://www.kodak.com/ek/uploadedFiles/Content/Small_Business/Images_Sensor_Solutions/Datasheets(pdfs)/KAF-39000LongSpec.pdf"><i>KODAK KAF-39000 IMAGE SENSOR, DEVICE PERFORMANCE SPECIFICATION</i></a>, KODAK, April 30, 2010<span class="reference-accessdate">, retrieved 2014-02-09</span></span><span title="ctx_ver=Z39.88-2004&amp;rfr_id=info%3Asid%2Fen.wikipedia.org%3AImage+sensor+format&amp;rft.btitle=KODAK+KAF-39000+IMAGE+SENSOR%2C+DEVICE+PERFORMANCE+SPECIFICATION&amp;rft.date=April+30%2C+2010&amp;rft.genre=book&amp;rft_id=http%3A%2F%2Fwww.kodak.com%2Fek%2FuploadedFiles%2FContent%2FSmall_Business%2FImages_Sensor_Solutions%2FDatasheets%28pdfs%29%2FKAF-39000LongSpec.pdf&amp;rft.pub=KODAK&amp;rft_val_fmt=info%3Aofi%2Ffmt%3Akev%3Amtx%3Abook" class="Z3988"><span style="display:none;">&#160;</span></span></span></li>
<li id="cite_note-30"><span class="mw-cite-backlink"><b><a href="#cite_ref-30">^</a></b></span> <span class="reference-text"><span id="CITEREF" class="citation"><a rel="nofollow" class="external text" href="http://www.bhphotovideo.com/c/product/893195-REG/Hasselblad_H5D_60_DSLR_Camera_With.html"><i>Hasselblad H5D-60 medium-format DSLR camera</i></a>, B&amp;H PHOTO VIDEO<span class="reference-accessdate">, retrieved 2013-06-19</span></span><span title="ctx_ver=Z39.88-2004&amp;rfr_id=info%3Asid%2Fen.wikipedia.org%3AImage+sensor+format&amp;rft.btitle=Hasselblad+H5D-60+medium-format+DSLR+camera&amp;rft.genre=book&amp;rft_id=http%3A%2F%2Fwww.bhphotovideo.com%2Fc%2Fproduct%2F893195-REG%2FHasselblad_H5D_60_DSLR_Camera_With.html&amp;rft.pub=B%26H+PHOTO+VIDEO&amp;rft_val_fmt=info%3Aofi%2Ffmt%3Akev%3Amtx%3Abook" class="Z3988"><span style="display:none;">&#160;</span></span></span></li>
</ol>
</div>
<h2><span class="mw-headline" id="External_links">External links</span></h2>
<ul>
<li>Eric Fossum: <a rel="nofollow" class="external text" href="http://www.youtube.com/watch?v=JkBh71zZKrM">Photons to Bits and Beyond: The Science &amp; Technology of Digital</a>, Oct. 13, 2011 (YouTube Video of lecture)</li>
<li>Joseph James: <a rel="nofollow" class="external text" href="http://www.josephjamesphotography.com/equivalence/">Equivalence</a> at Joseph James Photography</li>
<li>Simon Tindemans: <a rel="nofollow" class="external text" href="http://www.21stcenturyshoebox.com/essays/formatindependence/">Alternative photographic parameters: a format-independent approach</a> at 21stcenturyshoebox</li>
<li><a rel="nofollow" class="external text" href="http://web.archive.org/web/20110605233923/http://www.dpreview.com/articles/compactcamerahighiso/">Compact Camera High ISO modes: Separating the facts from the hype</a> at dpreview.com, May 2007</li>
<li><a rel="nofollow" class="external text" href="http://6mpixel.org/en/">The best compromise for a compact camera is a sensor with 6 million pixels or better a sensor with a pixel size of &gt;3µm</a> at 6mpixel.org</li>
</ul>


<!-- 
NewPP limit report
Parsed by mw1057
CPU time usage: 1.520 seconds
Real time usage: 1.823 seconds
Preprocessor visited node count: 2896/1000000
Preprocessor generated node count: 10163/1500000
Post‐expand include size: 52279/2048000 bytes
Template argument size: 2950/2048000 bytes
Highest expansion depth: 20/40
Expensive parser function count: 6/500
Lua time usage: 0.200/10.000 seconds
Lua memory usage: 5.76 MB/50 MB
-->
