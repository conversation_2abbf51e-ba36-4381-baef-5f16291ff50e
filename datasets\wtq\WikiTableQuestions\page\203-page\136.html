<!-- http%3A//en.wikipedia.org/wiki%3Faction%3Drender%26curid%3D218204%26oldid%3D601574823 2014-06-07-09-59-36 -->
<div class="hatnote">"PS/2" redirects here. For the video game console, see <a href="//en.wikipedia.org/wiki/PlayStation_2" title="PlayStation 2">PlayStation 2</a>.  For the keyboard and mouse connectors introduced with this system, see <a href="//en.wikipedia.org/wiki/PS/2_connector" title="PS/2 connector" class="mw-redirect">PS/2 connector</a>.</div>
<table class="metadata plainlinks ambox ambox-content ambox-Refimprove" role="presentation">
<tr>
<td class="mbox-image">
<div style="width:52px;"><a href="//en.wikipedia.org/wiki/File:Question_book-new.svg" class="image"><img alt="" src="//upload.wikimedia.org/wikipedia/en/thumb/9/99/Question_book-new.svg/50px-Question_book-new.svg.png" width="50" height="39" srcset="//upload.wikimedia.org/wikipedia/en/thumb/9/99/Question_book-new.svg/75px-Question_book-new.svg.png 1.5x, //upload.wikimedia.org/wikipedia/en/thumb/9/99/Question_book-new.svg/100px-Question_book-new.svg.png 2x" data-file-width="262" data-file-height="204" /></a></div>
</td>
<td class="mbox-text"><span class="mbox-text-span">This article <b>needs additional citations for <a href="//en.wikipedia.org/wiki/Wikipedia:Verifiability" title="Wikipedia:Verifiability">verification</a></b>. <span class="hide-when-compact">Please help <a class="external text" href="//en.wikipedia.org/w/index.php?title=IBM_Personal_System/2&amp;action=edit">improve this article</a> by <a href="//en.wikipedia.org/wiki/Help:Introduction_to_referencing/1" title="Help:Introduction to referencing/1">adding citations to reliable sources</a>. Unsourced material may be challenged and removed.</span> <small><i>(February 2011)</i></small></span></td>
</tr>
</table>
<table class="infobox hproduct vevent" cellspacing="3" style="border-spacing:3px;width:22em;">
<tr>
<td colspan="2" style="text-align:center;"><a href="//en.wikipedia.org/wiki/File:IBM_PS2_MCA_Model_55_SX,_front.jpg" class="image"><img alt="IBM PS2 MCA Model 55 SX, front.jpg" src="//upload.wikimedia.org/wikipedia/commons/thumb/4/4a/IBM_PS2_MCA_Model_55_SX%2C_front.jpg/200px-IBM_PS2_MCA_Model_55_SX%2C_front.jpg" width="200" height="150" srcset="//upload.wikimedia.org/wikipedia/commons/thumb/4/4a/IBM_PS2_MCA_Model_55_SX%2C_front.jpg/300px-IBM_PS2_MCA_Model_55_SX%2C_front.jpg 1.5x, //upload.wikimedia.org/wikipedia/commons/thumb/4/4a/IBM_PS2_MCA_Model_55_SX%2C_front.jpg/400px-IBM_PS2_MCA_Model_55_SX%2C_front.jpg 2x" data-file-width="640" data-file-height="480" /></a>
<div>IBM Personal System/2 Model 55 SX</div>
</td>
</tr>
<tr>
<th scope="row" style="text-align:left;">Developer</th>
<td><a href="//en.wikipedia.org/wiki/IBM" title="IBM">International Business Machines Corporation (IBM)</a></td>
</tr>
<tr>
<th scope="row" style="text-align:left;">Type</th>
<td>Professional Computer</td>
</tr>
<tr>
<th scope="row" style="text-align:left;">Release date</th>
<td>April&#160;1987<span style="display:none">&#160;(<span class="bday dtstart published updated">1987-04</span>)</span></td>
</tr>
<tr>
<th scope="row" style="text-align:left;">Predecessor</th>
<td><a href="//en.wikipedia.org/wiki/IBM_Personal_Computer/AT" title="IBM Personal Computer/AT">IBM Personal Computer/AT</a></td>
</tr>
<tr>
<th scope="row" style="text-align:left;">Successor</th>
<td><a href="//en.wikipedia.org/wiki/IBM_PS/ValuePoint" title="IBM PS/ValuePoint">IBM PS/ValuePoint</a></td>
</tr>
</table>
<p>The <b>Personal System/2</b> or <b>PS/2</b> was <a href="//en.wikipedia.org/wiki/IBM" title="IBM">IBM</a>'s third generation of <a href="//en.wikipedia.org/wiki/Personal_computer" title="Personal computer">personal computers</a> released in 1987. The PS/2 line was created by IBM in an attempt to recapture control of the PC market by introducing an advanced yet <a href="//en.wikipedia.org/wiki/Vendor_lock-in" title="Vendor lock-in">proprietary</a> architecture. IBM's considerable market presence plus the reliability of the PS/2 ensured that the systems would sell in relatively large numbers, especially to large businesses. However the other major manufacturers balked at IBM's licensing terms to develop and sell compatible hardware, particularly as the demanded royalties were on a per machine basis. Also the evolving <a href="//en.wikipedia.org/wiki/Wintel" title="Wintel">Wintel</a> architecture was seeing a period of dramatic reductions in price, and so these developments prevented the PS/2 from returning control of the PC market to IBM.</p>
<p>Due to the higher costs of the architecture, customers preferred competing PCs that extended the existing PC architecture instead of abandoning it for something new. However, many of the PS/2's innovations, such as the <a href="//en.wikipedia.org/wiki/16550_UART" title="16550 UART">16550</a> <a href="//en.wikipedia.org/wiki/UART" title="UART" class="mw-redirect">UART</a> (serial port), 1440 KB 3.5-inch <a href="//en.wikipedia.org/wiki/Floppy_disk" title="Floppy disk">floppy disk</a> format, 72-pin <a href="//en.wikipedia.org/wiki/SIMM" title="SIMM">SIMMs</a>, the <a href="#Keyboard.2Fmouse">PS/2 keyboard and mouse ports</a>, and the <a href="#Graphics">VGA video standard</a>, went on to become standards in the broader PC market.</p>
<p>The <a href="//en.wikipedia.org/wiki/OS/2" title="OS/2">OS/2</a> <a href="//en.wikipedia.org/wiki/Operating_system" title="Operating system">operating system</a> was announced at the same time as the PS/2 line and was intended to be the primary operating system for models with <a href="//en.wikipedia.org/wiki/Intel_286" title="Intel 286" class="mw-redirect">Intel 286</a> or later processors. However, at the time of the first shipments, only <a href="//en.wikipedia.org/wiki/PC_DOS" title="PC DOS" class="mw-redirect">PC DOS</a> was available. OS/2 1.0 (text-mode only) and Microsoft's <a href="//en.wikipedia.org/wiki/Windows_2.0" title="Windows 2.0">Windows 2.0</a> became available several months later. IBM also released <a href="//en.wikipedia.org/wiki/IBM_AIX_(operating_system)" title="IBM AIX (operating system)" class="mw-redirect">AIX</a> PS/2, a <a href="//en.wikipedia.org/wiki/UNIX" title="UNIX" class="mw-redirect">UNIX</a> operating system for PS/2 models with <a href="//en.wikipedia.org/wiki/Intel_386" title="Intel 386" class="mw-redirect">Intel 386</a> or later processors.</p>
<div class="thumb tright">
<div class="thumbinner" style="width:222px;">
<div class="mediaContainer" style="position:relative;display:block;width:220px"><audio id="mwe_player_0" style="width:220px;height:23px" poster="//bits.wikimedia.org/static-1.24wmf7/skins/common/images/icons/fileicon-ogg.png" controls="" preload="none" class="kskin" data-durationhint="64.078895833333" data-startoffset="0" data-mwtitle="IBM_Personal_System2_Intel_286_computer.ogg" data-mwprovider="wikimediacommons"><source src="//upload.wikimedia.org/wikipedia/commons/a/a3/IBM_Personal_System2_Intel_286_computer.ogg" type="audio/ogg; codecs=&quot;vorbis&quot;" data-title="Original Ogg file (180 kbps)" data-shorttitle="Ogg source" data-width="0" data-height="0" data-bandwidth="179917"></source>Sorry, your browser either has JavaScript disabled or does not have any supported player.<br />
You can <a href="//upload.wikimedia.org/wikipedia/commons/a/a3/IBM_Personal_System2_Intel_286_computer.ogg">download the clip</a> or <a href="https://www.mediawiki.org/wiki/Special:MyLanguage/Extension:TimedMediaHandler/Client_download">download a player</a> to play the clip in your browser.</audio></div>
<div class="thumbcaption">
<div class="magnify"><a href="//en.wikipedia.org/wiki/File:IBM_Personal_System2_Intel_286_computer.ogg" class="internal" title="Enlarge"><img src="//bits.wikimedia.org/static-1.24wmf7/skins/common/images/magnify-clip.png" width="15" height="11" alt="" /></a></div>
IBM Personal System/2 Model 30 286. <a href="//en.wikipedia.org/wiki/Power-on_self-test" title="Power-on self-test">Power-on self-test</a>, <a href="//en.wikipedia.org/wiki/Bootstrapping_(computing)" title="Bootstrapping (computing)" class="mw-redirect">bootstrapping</a>, power-off</div>
</div>
</div>
<p></p>
<div id="toc" class="toc">
<div id="toctitle">
<h2>Contents</h2>
</div>
<ul>
<li class="toclevel-1 tocsection-1"><a href="#Technology"><span class="tocnumber">1</span> <span class="toctext">Technology</span></a>
<ul>
<li class="toclevel-2 tocsection-2"><a href="#Micro_Channel_Architecture"><span class="tocnumber">1.1</span> <span class="toctext">Micro Channel Architecture</span></a></li>
<li class="toclevel-2 tocsection-3"><a href="#Keyboard.2Fmouse"><span class="tocnumber">1.2</span> <span class="toctext">Keyboard/mouse</span></a>
<ul>
<li class="toclevel-3 tocsection-4"><a href="#Layout"><span class="tocnumber">1.2.1</span> <span class="toctext">Layout</span></a></li>
<li class="toclevel-3 tocsection-5"><a href="#Interface"><span class="tocnumber">1.2.2</span> <span class="toctext">Interface</span></a></li>
</ul>
</li>
<li class="toclevel-2 tocsection-6"><a href="#Graphics"><span class="tocnumber">1.3</span> <span class="toctext">Graphics</span></a>
<ul>
<li class="toclevel-3 tocsection-7"><a href="#VGA_video_connector"><span class="tocnumber">1.3.1</span> <span class="toctext">VGA video connector</span></a></li>
</ul>
</li>
<li class="toclevel-2 tocsection-8"><a href="#Storage"><span class="tocnumber">1.4</span> <span class="toctext">Storage</span></a></li>
<li class="toclevel-2 tocsection-9"><a href="#Memory"><span class="tocnumber">1.5</span> <span class="toctext">Memory</span></a></li>
<li class="toclevel-2 tocsection-10"><a href="#Models"><span class="tocnumber">1.6</span> <span class="toctext">Models</span></a></li>
</ul>
</li>
<li class="toclevel-1 tocsection-11"><a href="#Marketing"><span class="tocnumber">2</span> <span class="toctext">Marketing</span></a></li>
<li class="toclevel-1 tocsection-12"><a href="#See_also"><span class="tocnumber">3</span> <span class="toctext">See also</span></a></li>
<li class="toclevel-1 tocsection-13"><a href="#References"><span class="tocnumber">4</span> <span class="toctext">References</span></a></li>
<li class="toclevel-1 tocsection-14"><a href="#Further_reading"><span class="tocnumber">5</span> <span class="toctext">Further reading</span></a></li>
<li class="toclevel-1 tocsection-15"><a href="#External_links"><span class="tocnumber">6</span> <span class="toctext">External links</span></a></li>
</ul>
</div>
<p></p>
<h2><span class="mw-headline" id="Technology">Technology</span></h2>
<p>For years before IBM released the PS/2, rumors spread about IBM's plans for successors to its <a href="//en.wikipedia.org/wiki/IBM_PC" title="IBM PC" class="mw-redirect">IBM PC</a>, <a href="//en.wikipedia.org/wiki/IBM_PC_XT" title="IBM PC XT" class="mw-redirect">XT</a>, and <a href="//en.wikipedia.org/wiki/IBM_PC_AT" title="IBM PC AT" class="mw-redirect">AT</a> personal computers. Among the rumors that did not come true:<sup id="cite_ref-byte198706_1-0" class="reference"><a href="#cite_note-byte198706-1"><span>[</span>1<span>]</span></a></sup></p>
<ul>
<li>The company would use proprietary, hard-to-copy versions of the Intel <a href="//en.wikipedia.org/wiki/80286" title="80286" class="mw-redirect">80286</a> and <a href="//en.wikipedia.org/wiki/80386" title="80386" class="mw-redirect">80386</a> processors.</li>
<li>The company would release a version of its <a href="//en.wikipedia.org/wiki/VM_(operating_system)" title="VM (operating system)">VM</a> mainframe operating system for them.</li>
<li>The company would design the new computers to make third-party communications products more difficult to design.</li>
</ul>
<p>IBM's PS/2 was designed to remain software compatible with their PC/AT/XT line of computers upon which the large <a href="//en.wikipedia.org/wiki/PC_clone" title="PC clone" class="mw-redirect">PC clone</a> market was built, but the hardware was quite different. PS/2 had two <a href="//en.wikipedia.org/wiki/BIOS" title="BIOS">BIOSes</a>; one was named ABIOS (Advanced BIOS) which provided a new <a href="//en.wikipedia.org/wiki/Protected_mode" title="Protected mode">protected mode</a> interface and was used by OS/2, and the other was named CBIOS (Compatible BIOS) which was included in order for the PS/2 to be software compatible with the PC/AT/XT. CBIOS was so compatible that it even included <a href="//en.wikipedia.org/wiki/Cassette_BASIC" title="Cassette BASIC" class="mw-redirect">Cassette BASIC</a>. While IBM did not publish the BIOS source code, it did promise to publish BIOS <a href="//en.wikipedia.org/wiki/Entry_point" title="Entry point">entry points</a>.<sup id="cite_ref-byte198706_1-1" class="reference"><a href="#cite_note-byte198706-1"><span>[</span>1<span>]</span></a></sup></p>
<h3><span class="mw-headline" id="Micro_Channel_Architecture">Micro Channel Architecture</span></h3>
<div class="hatnote relarticle mainarticle">Main article: <a href="//en.wikipedia.org/wiki/Micro_Channel_Architecture" title="Micro Channel Architecture" class="mw-redirect">Micro Channel Architecture</a></div>
<p>With the IBM PS/2 line, <a href="//en.wikipedia.org/wiki/Micro_Channel_Architecture" title="Micro Channel Architecture" class="mw-redirect">Micro Channel Architecture</a> (MCA) was also introduced.<sup id="cite_ref-byte198706_1-2" class="reference"><a href="#cite_note-byte198706-1"><span>[</span>1<span>]</span></a></sup> MCA was conceptually similar to the channel architecture of the <a href="//en.wikipedia.org/wiki/IBM_360" title="IBM 360" class="mw-redirect">IBM 360</a> mainframes. MCA was technically superior to <a href="//en.wikipedia.org/wiki/Industry_Standard_Architecture" title="Industry Standard Architecture">ISA</a> and allowed for higher speed communications within the system. MCA featured many advances not seen in other standards until several years later. Transfer speeds were on par with the much later <a href="//en.wikipedia.org/wiki/Peripheral_Component_Interconnect" title="Peripheral Component Interconnect" class="mw-redirect">PCI</a> standard. MCA allowed one-to-one, card to card, and multi-card to processor simultaneous transaction management which is a feature of the <a href="//en.wikipedia.org/wiki/PCI-X" title="PCI-X">PCI-X</a> bus format.</p>
<p>Bus mastering capability, bus arbitration, and a primitive form of <a href="//en.wikipedia.org/wiki/Plug-and-play" title="Plug-and-play" class="mw-redirect">plug-and-play</a> BIOS management of hardware were all benefits of MCA. (One book from the year 2000 writes: "MCA used an early (and user-hostile) version of what we know now as “Plug-N′-Play”, requiring a special setup disk for each machine and each card."<sup id="cite_ref-Held2000_2-0" class="reference"><a href="#cite_note-Held2000-2"><span>[</span>2<span>]</span></a></sup> MCA never gained wide acceptance outside of the PS/2 line due to IBM's anti-clone practices<sup class="noprint Inline-Template Template-Fact" style="white-space:nowrap;">[<i><a href="//en.wikipedia.org/wiki/Wikipedia:Citation_needed" title="Wikipedia:Citation needed"><span title="This claim needs references to reliable sources. (November 2011)">citation needed</span></a></i>]</sup> and incompatibilities with <a href="//en.wikipedia.org/wiki/Industry_Standard_Architecture" title="Industry Standard Architecture">ISA</a>. IBM offered to sell an MCA license to anyone who could afford the royalty. However, royalties were required for every MCA-compatible machine sold and a payment for every IBM-compatible machine the particular maker had made in the past.<sup class="noprint Inline-Template Template-Fact" style="white-space:nowrap;">[<i><a href="//en.wikipedia.org/wiki/Wikipedia:Citation_needed" title="Wikipedia:Citation needed"><span title="This claim needs references to reliable sources. (November 2009)">citation needed</span></a></i>]</sup></p>
<h3><span class="mw-headline" id="Keyboard.2Fmouse">Keyboard/mouse</span></h3>
<h4><span class="mw-headline" id="Layout">Layout</span></h4>
<p>The PS/2 <a href="//en.wikipedia.org/wiki/IBM_Model_M_keyboard" title="IBM Model M keyboard" class="mw-redirect">IBM Model M keyboard</a> used the same 101-key layout of the previous <a href="//en.wikipedia.org/wiki/IBM_PC/AT" title="IBM PC/AT" class="mw-redirect">IBM PC/AT</a> Extended keyboard, itself derived from the original <a href="//en.wikipedia.org/wiki/IBM_PC" title="IBM PC" class="mw-redirect">IBM PC</a> keyboard.<sup id="cite_ref-byte198706_1-3" class="reference"><a href="#cite_note-byte198706-1"><span>[</span>1<span>]</span></a></sup> European variants had 102 keys with the addition of an extra key to the right of the left Shift key. The Model M, using a buckling spring mechanism, is still being manufactured by Unicomp.</p>
<h4><span class="mw-headline" id="Interface">Interface</span></h4>
<div class="hatnote boilerplate seealso">For more details on this topic, see <a href="//en.wikipedia.org/wiki/PS/2_connector" title="PS/2 connector" class="mw-redirect">PS/2 connector</a>.</div>
<div class="thumb tleft">
<div class="thumbinner" style="width:172px;"><a href="//en.wikipedia.org/wiki/File:IBM_PS2_hiiri.jpg" class="image"><img alt="" src="//upload.wikimedia.org/wikipedia/commons/thumb/6/6b/IBM_PS2_hiiri.jpg/170px-IBM_PS2_hiiri.jpg" width="170" height="298" class="thumbimage" srcset="//upload.wikimedia.org/wikipedia/commons/thumb/6/6b/IBM_PS2_hiiri.jpg/255px-IBM_PS2_hiiri.jpg 1.5x, //upload.wikimedia.org/wikipedia/commons/thumb/6/6b/IBM_PS2_hiiri.jpg/340px-IBM_PS2_hiiri.jpg 2x" data-file-width="565" data-file-height="989" /></a>
<div class="thumbcaption">
<div class="magnify"><a href="//en.wikipedia.org/wiki/File:IBM_PS2_hiiri.jpg" class="internal" title="Enlarge"><img src="//bits.wikimedia.org/static-1.24wmf7/skins/common/images/magnify-clip.png" width="15" height="11" alt="" /></a></div>
The original IBM PS/2 mouse.</div>
</div>
</div>
<div class="thumb tright">
<div class="thumbinner" style="width:222px;"><a href="//en.wikipedia.org/wiki/File:Ps-2-ports.jpg" class="image"><img alt="" src="//upload.wikimedia.org/wikipedia/commons/thumb/8/83/Ps-2-ports.jpg/220px-Ps-2-ports.jpg" width="220" height="89" class="thumbimage" srcset="//upload.wikimedia.org/wikipedia/commons/thumb/8/83/Ps-2-ports.jpg/330px-Ps-2-ports.jpg 1.5x, //upload.wikimedia.org/wikipedia/commons/thumb/8/83/Ps-2-ports.jpg/440px-Ps-2-ports.jpg 2x" data-file-width="446" data-file-height="180" /></a>
<div class="thumbcaption">
<div class="magnify"><a href="//en.wikipedia.org/wiki/File:Ps-2-ports.jpg" class="internal" title="Enlarge"><img src="//bits.wikimedia.org/static-1.24wmf7/skins/common/images/magnify-clip.png" width="15" height="11" alt="" /></a></div>
The PS/2 connection ports (later colored purple for keyboard and green for mouse —<a href="//en.wikipedia.org/wiki/PC_97" title="PC 97" class="mw-redirect">PC 97</a>) were once commonly used for connecting input devices.</div>
</div>
</div>
<p>PS/2 systems introduced a new specification for the <a href="//en.wikipedia.org/wiki/Computer_keyboard" title="Computer keyboard">keyboard</a> and <a href="//en.wikipedia.org/wiki/Computer_mouse" title="Computer mouse" class="mw-redirect">mouse</a> interfaces, which are still in use today and are thus called "PS/2" interfaces. The PS/2 keyboard interface was electronically identical to the long-established <a href="//en.wikipedia.org/wiki/AT_Keyboard" title="AT Keyboard" class="mw-redirect">AT</a> interface, but the cable connector was changed from the 5-pin <a href="//en.wikipedia.org/wiki/DIN_connector" title="DIN connector">DIN connector</a> to the smaller 6-pin <a href="//en.wikipedia.org/wiki/Mini-DIN_connector" title="Mini-DIN connector">mini-DIN</a> interface. The same connector and a similar synchronous serial interface was used for the PS/2 mouse port.</p>
<p>Additionally, the PS/2 introduced a new software data area known as the Extended BIOS Data Area (EBDA). Its primary use was to add a new buffer area for the dedicated mouse port. This also required making a change to the "traditional" BIOS Data Area (BDA) which was then required to point to the base address of the EBDA.</p>
<h3><span class="mw-headline" id="Graphics">Graphics</span></h3>
<p>Most of the initial range of PS/2 models were equipped with a new <a href="//en.wikipedia.org/wiki/Frame_buffer" title="Frame buffer" class="mw-redirect">frame buffer</a> known as the <a href="//en.wikipedia.org/wiki/Video_Graphics_Array" title="Video Graphics Array">Video Graphics Array</a>, or VGA for short. This effectively replaced the previous <a href="//en.wikipedia.org/wiki/Enhanced_Graphics_Adapter" title="Enhanced Graphics Adapter">EGA</a> standard.<sup id="cite_ref-byte198706_1-4" class="reference"><a href="#cite_note-byte198706-1"><span>[</span>1<span>]</span></a></sup> VGA increased graphics memory to 256 KB and provided for resolutions of 640×480 with 16 colors, and 320×200 with 256 colors. VGA also provided a palette of 262,144 colors (as opposed to the EGA palette of 64 colors). The <a href="//en.wikipedia.org/wiki/IBM_8514" title="IBM 8514">IBM 8514</a> and later <a href="//en.wikipedia.org/wiki/XGA" title="XGA" class="mw-redirect">XGA</a> <a href="//en.wikipedia.org/wiki/Computer_display_standard" title="Computer display standard">computer display standards</a> were also introduced on the PS/2 line.</p>
<p>Although the design of these adapters did not become an industry standard as VGA did, their 1024×768 pixel resolution was subsequently widely adopted as a standard by other manufacturers, and "XGA" became a synonym for this screen resolution. The PS/2 Model 25 and Model 30, however, did not include VGA. On these budget models, IBM opted to use <a href="//en.wikipedia.org/wiki/Multicolor_Graphics_Adapter" title="Multicolor Graphics Adapter" class="mw-redirect">MCGA</a>, which was a stepping stone between <a href="//en.wikipedia.org/wiki/Color_Graphics_Adapter" title="Color Graphics Adapter">CGA</a> and VGA, but unfortunately lacked EGA compatibility.</p>
<div class="thumb tright">
<div class="thumbinner" style="width:222px;"><a href="//en.wikipedia.org/wiki/File:MCA_IBM_XGA-2.jpg" class="image"><img alt="" src="//upload.wikimedia.org/wikipedia/commons/thumb/d/d6/MCA_IBM_XGA-2.jpg/220px-MCA_IBM_XGA-2.jpg" width="220" height="72" class="thumbimage" srcset="//upload.wikimedia.org/wikipedia/commons/thumb/d/d6/MCA_IBM_XGA-2.jpg/330px-MCA_IBM_XGA-2.jpg 1.5x, //upload.wikimedia.org/wikipedia/commons/thumb/d/d6/MCA_IBM_XGA-2.jpg/440px-MCA_IBM_XGA-2.jpg 2x" data-file-width="1280" data-file-height="417" /></a>
<div class="thumbcaption">
<div class="magnify"><a href="//en.wikipedia.org/wiki/File:MCA_IBM_XGA-2.jpg" class="internal" title="Enlarge"><img src="//bits.wikimedia.org/static-1.24wmf7/skins/common/images/magnify-clip.png" width="15" height="11" alt="" /></a></div>
MCA IBM XGA-2 Graphics Card</div>
</div>
</div>
<h4><span class="mw-headline" id="VGA_video_connector">VGA video connector</span></h4>
<p>All of the new PS/2 graphics systems (whether MCGA, VGA, 8514, or later XGA) used a 15-pin <a href="//en.wikipedia.org/wiki/D-sub" title="D-sub" class="mw-redirect">D-sub</a> connector for video out. This used analog RGB signals, rather than fixed sixteen or sixty-four color lines as on previous CGA and EGA monitors, allowing arbitrary increases in the color depth (or levels of grey) compared to its predecessors. It also allowed for analog grayscale displays to be connected; unlike earlier systems (like <a href="//en.wikipedia.org/wiki/IBM_Monochrome_Display_Adapter" title="IBM Monochrome Display Adapter">MDA</a> and <a href="//en.wikipedia.org/wiki/Hercules_Graphics_Card" title="Hercules Graphics Card">Hercules</a>) this was transparent to software, allowing all programs supporting the new standards to run unmodified whichever type of display was attached. (On the other hand, whether the display was color or monochrome was undetectable to software, so selection between application displays optimized for color or monochrome, in applications that supported both, required user intervention.) These greyscale displays were relatively inexpensive during the first few years the PS/2 was available, and they were very commonly purchased with lower-end models.</p>
<p>The <a href="//en.wikipedia.org/wiki/VGA_connector" title="VGA connector">VGA connector</a> became the de facto standard for connecting monitors and projectors on both PC and non-PC hardware over the course of the early 1990s, replacing a variety of earlier connectors.</p>
<h3><span class="mw-headline" id="Storage">Storage</span></h3>
<div class="thumb tright">
<div class="thumbinner" style="width:222px;"><a href="//en.wikipedia.org/wiki/File:IBM_PS2_MCA_Model_70,_riser_for_floppy_and_hard_drive.jpg" class="image"><img alt="" src="//upload.wikimedia.org/wikipedia/commons/thumb/5/59/IBM_PS2_MCA_Model_70%2C_riser_for_floppy_and_hard_drive.jpg/220px-IBM_PS2_MCA_Model_70%2C_riser_for_floppy_and_hard_drive.jpg" width="220" height="165" class="thumbimage" srcset="//upload.wikimedia.org/wikipedia/commons/thumb/5/59/IBM_PS2_MCA_Model_70%2C_riser_for_floppy_and_hard_drive.jpg/330px-IBM_PS2_MCA_Model_70%2C_riser_for_floppy_and_hard_drive.jpg 1.5x, //upload.wikimedia.org/wikipedia/commons/thumb/5/59/IBM_PS2_MCA_Model_70%2C_riser_for_floppy_and_hard_drive.jpg/440px-IBM_PS2_MCA_Model_70%2C_riser_for_floppy_and_hard_drive.jpg 2x" data-file-width="640" data-file-height="480" /></a>
<div class="thumbcaption">
<div class="magnify"><a href="//en.wikipedia.org/wiki/File:IBM_PS2_MCA_Model_70,_riser_for_floppy_and_hard_drive.jpg" class="internal" title="Enlarge"><img src="//bits.wikimedia.org/static-1.24wmf7/skins/common/images/magnify-clip.png" width="15" height="11" alt="" /></a></div>
Some PS/2 models used a quick-attachment socket on the back of the floppy drive. This connector is incompatible with a standard 5.25" floppy connector because IBM also supplied power to the drive via the connector, which was not done for the 5.25" drive. Also shown on the right is the special IBM-only hard drive which incorporates power and data into a single connector. PS/2 power supplies typically did not have additional spare 4-pin power connectors for use with internal storage.</div>
</div>
</div>
<div class="thumb tleft">
<div class="thumbinner" style="width:222px;"><a href="//en.wikipedia.org/wiki/File:IBM_PS2_MCA_Model_55_SX,_hard_drive_bus_closeup.jpg" class="image"><img alt="" src="//upload.wikimedia.org/wikipedia/commons/thumb/e/e0/IBM_PS2_MCA_Model_55_SX%2C_hard_drive_bus_closeup.jpg/220px-IBM_PS2_MCA_Model_55_SX%2C_hard_drive_bus_closeup.jpg" width="220" height="165" class="thumbimage" srcset="//upload.wikimedia.org/wikipedia/commons/thumb/e/e0/IBM_PS2_MCA_Model_55_SX%2C_hard_drive_bus_closeup.jpg/330px-IBM_PS2_MCA_Model_55_SX%2C_hard_drive_bus_closeup.jpg 1.5x, //upload.wikimedia.org/wikipedia/commons/thumb/e/e0/IBM_PS2_MCA_Model_55_SX%2C_hard_drive_bus_closeup.jpg/440px-IBM_PS2_MCA_Model_55_SX%2C_hard_drive_bus_closeup.jpg 2x" data-file-width="640" data-file-height="480" /></a>
<div class="thumbcaption">
<div class="magnify"><a href="//en.wikipedia.org/wiki/File:IBM_PS2_MCA_Model_55_SX,_hard_drive_bus_closeup.jpg" class="internal" title="Enlarge"><img src="//bits.wikimedia.org/static-1.24wmf7/skins/common/images/magnify-clip.png" width="15" height="11" alt="" /></a></div>
Close-up view of unusual 72-pin MCA internal hard drive connector.</div>
</div>
</div>
<p>Although 3.5" <a href="//en.wikipedia.org/wiki/Floppy_disk" title="Floppy disk">floppy disks</a> were becoming common in the industry by 1987, the PS/2s were the first desktop IBM models to use them as standard. This itself was a positive move, and soon widely followed by the rest of the industry, but the lack of any 5.25" internal bays in the PS/2 cases meant that as <a href="//en.wikipedia.org/wiki/CD-ROM#Standard" title="CD-ROM">CD-ROM drives</a> based on that form factor were adopted, PS/2 users had to add an external device using the expensive <a href="//en.wikipedia.org/wiki/SCSI" title="SCSI">SCSI</a> interface.<sup id="cite_ref-3" class="reference"><a href="#cite_note-3"><span>[</span>3<span>]</span></a></sup></p>
<p>While the 3.5" disk format itself was standard, IBM chose to use a non-standard form for the disk drives, resulting in very high repair costs as a standard drive could not be retrofitted to a PS/2. The IBM part was functionally identical to a standard 3.5" floppy drive. In the initial line-up, IBM used 720&#160;KB double density (DD) capacity drives on the 8086-based models and 1.44&#160;MB high density (HD) on the 80286-based and higher models. By the end of the PS/2 line they had moved to a somewhat standardized capacity of 2.88&#160;MB.</p>
<p>The PS/2 floppy drives were famous for not having a capacity detector. 1.44&#160;MB floppies had a hole so that drives could identify them from 720&#160;KB floppies, preventing users from formatting the smaller capacity disks to the higher capacity (doing so would work, but with a higher tendency of data loss). Clone manufacturers implemented the hole detection, but IBM did not. As a result of this a 720 KB floppy could be formatted to 1.44&#160;MB in a PS/2, but the resulting floppy would only be readable by a PS/2 machine.<sup id="cite_ref-4" class="reference"><a href="#cite_note-4"><span>[</span>4<span>]</span></a></sup></p>
<p>The PS/2 used several different types of internal hard drives. Some models used <a href="//en.wikipedia.org/wiki/AT_attachment" title="AT attachment" class="mw-redirect">ATA/IDE</a> while others used a special custom-interface drive commonly referred to as an ESDI drive, but which incorporated power and data into a single connector, as shown in the photo to the right. Typically the PS/2 only permitted use of one hard drive inside the computer case. Additional storage was attached externally, using the optional <a href="//en.wikipedia.org/wiki/SCSI" title="SCSI">SCSI</a> interface.</p>
<h3><span class="mw-headline" id="Memory">Memory</span></h3>
<p>The PS/2 introduced the <a href="//en.wikipedia.org/wiki/SIMM#72-pin_SIMMs" title="SIMM">72-pin SIMM</a><sup class="noprint Inline-Template Template-Fact" style="white-space:nowrap;">[<i><a href="//en.wikipedia.org/wiki/Wikipedia:Citation_needed" title="Wikipedia:Citation needed"><span title="This claim needs references to reliable sources. (January 2013)">citation needed</span></a></i>]</sup> which became the <a href="//en.wikipedia.org/wiki/De_facto" title="De facto">de facto</a> standard for <a href="//en.wikipedia.org/wiki/RAM" title="RAM" class="mw-redirect">RAM</a> modules by the mid-90s in mid-to-late <a href="//en.wikipedia.org/wiki/Intel_80486" title="Intel 80486">486</a> and early <a href="//en.wikipedia.org/wiki/Intel_P5" title="Intel P5" class="mw-redirect">Pentium</a> desktop systems. 72-pin SIMMs were 32/36 bits wide and replaced the old <a href="//en.wikipedia.org/wiki/SIMM#30-pin_SIMMs" title="SIMM">30-pin SIMM</a> (8/9-bit) standard. The older SIMMs were much less convenient because they had to be installed in sets of two or four to match the width of the CPU's 16-bit (Intel 80286 and <a href="//en.wikipedia.org/wiki/Intel_386#The_i386SX_variant" title="Intel 386" class="mw-redirect">80386SX</a>) or 32-bit (80386 and <a href="//en.wikipedia.org/wiki/80486" title="80486" class="mw-redirect">80486</a>) bus. 72-pin SIMMs were also made with greater capacities.</p>
<h3><span class="mw-headline" id="Models">Models</span></h3>
<p>At launch, the PS/2 family comprised the Model 30, 50, 60 and 80;<sup id="cite_ref-byte198706_1-5" class="reference"><a href="#cite_note-byte198706-1"><span>[</span>1<span>]</span></a></sup> the Model 25 was launched a few months later.</p>
<p>The PS/2 <i>Models 25</i> and <i>30</i> (IBM 8525 and 8530 respectively) were similar to the <a href="//en.wikipedia.org/wiki/IBM_Personal_Computer" title="IBM Personal Computer">IBM PC</a>. They incorporated the <a href="//en.wikipedia.org/wiki/Industry_Standard_Architecture" title="Industry Standard Architecture">ISA bus</a> and the <a href="//en.wikipedia.org/wiki/Intel_8086" title="Intel 8086">Intel 8086</a> CPU in a different <a href="//en.wikipedia.org/wiki/Computer_form_factor" title="Computer form factor">form factor</a>. These machines also differed from other PS/2 models in having 720k floppy disk drives,<sup id="cite_ref-byte198706_1-6" class="reference"><a href="#cite_note-byte198706-1"><span>[</span>1<span>]</span></a></sup> an <a href="//en.wikipedia.org/wiki/ST506" title="ST506" class="mw-redirect">ST506</a>-compatible hard drive controller and <a href="//en.wikipedia.org/wiki/Multicolor_Graphics_Adapter" title="Multicolor Graphics Adapter" class="mw-redirect">MCGA</a> graphics. The hard drives were available as an optional part; however, many of these entry-level machines were sold without hard drives due to the high cost.</p>
<div class="thumb tright">
<div class="thumbinner" style="width:222px;"><a href="//en.wikipedia.org/wiki/File:IBM_Personal_System2_Model_25.jpg" class="image"><img alt="" src="//upload.wikimedia.org/wikipedia/commons/thumb/b/b6/IBM_Personal_System2_Model_25.jpg/220px-IBM_Personal_System2_Model_25.jpg" width="220" height="250" class="thumbimage" srcset="//upload.wikimedia.org/wikipedia/commons/thumb/b/b6/IBM_Personal_System2_Model_25.jpg/330px-IBM_Personal_System2_Model_25.jpg 1.5x, //upload.wikimedia.org/wikipedia/commons/thumb/b/b6/IBM_Personal_System2_Model_25.jpg/440px-IBM_Personal_System2_Model_25.jpg 2x" data-file-width="600" data-file-height="681" /></a>
<div class="thumbcaption">
<div class="magnify"><a href="//en.wikipedia.org/wiki/File:IBM_Personal_System2_Model_25.jpg" class="internal" title="Enlarge"><img src="//bits.wikimedia.org/static-1.24wmf7/skins/common/images/magnify-clip.png" width="15" height="11" alt="" /></a></div>
IBM Personal System/2 Model 25</div>
</div>
</div>
<p>The Model 25 featured an integrated <a href="//en.wikipedia.org/wiki/Monochrome_monitor" title="Monochrome monitor">monochrome</a> or color monitor to compete with the <a href="//en.wikipedia.org/wiki/Apple_Macintosh" title="Apple Macintosh" class="mw-redirect">Apple Macintosh</a> as an integrated system for educational environments. An external "paper white" monochrome screen was also available as a cost-saving model instead of the standard VGA display; this was often paired with the Model 30.</p>
<p>Later ISA PS/2 models comprised the <i>Model 30-286</i> (a Model 30 with an <a href="//en.wikipedia.org/wiki/Intel_286" title="Intel 286" class="mw-redirect">Intel 286</a> CPU), <i>Model 35</i> (IBM 8535) and <i>Model 40</i> (IBM 8540) with <a href="//en.wikipedia.org/wiki/Intel_80386" title="Intel 80386">Intel 386SX</a> or IBM <a href="//en.wikipedia.org/wiki/386SLC" title="386SLC" class="mw-redirect">386SLC</a> processors.</p>
<p>The higher-numbered models were equipped with the Micro Channel bus and mostly <a href="//en.wikipedia.org/wiki/Enhanced_Small_Disk_Interface" title="Enhanced Small Disk Interface">ESDI</a> or <a href="//en.wikipedia.org/wiki/SCSI" title="SCSI">SCSI</a> <a href="//en.wikipedia.org/wiki/Hard_drive" title="Hard drive" class="mw-redirect">hard drives</a> (models 60-041 and 80-041 had <a href="//en.wikipedia.org/wiki/Modified_Frequency_Modulation" title="Modified Frequency Modulation">MFM</a> hard drives). PS/2 <i>Models 50</i> (IBM 8550) and <i>60</i> (IBM 8560) used the <a href="//en.wikipedia.org/wiki/Intel_80286" title="Intel 80286">Intel 286</a> processor, the PS/2 <i>Models 70</i> (IBM 8570) and <i>80</i> used the <a href="//en.wikipedia.org/wiki/Intel_80386" title="Intel 80386">386DX</a>, while the medium-performance PS/2 <i>Model 55SX</i> (IBM 8555-081) used the 16/32-bit 386SX processor. Later Model 70 and 80 variants (B-xx) also used 25&#160;MHz <a href="//en.wikipedia.org/wiki/Intel_80486" title="Intel 80486">Intel 486</a> processors, in a complex called the Power Platform.</p>
<div class="thumb tright">
<div class="thumbinner" style="width:222px;"><a href="//en.wikipedia.org/wiki/File:DeuxPS2.jpg" class="image"><img alt="" src="//upload.wikimedia.org/wikipedia/commons/thumb/e/ec/DeuxPS2.jpg/220px-DeuxPS2.jpg" width="220" height="310" class="thumbimage" srcset="//upload.wikimedia.org/wikipedia/commons/thumb/e/ec/DeuxPS2.jpg/330px-DeuxPS2.jpg 1.5x, //upload.wikimedia.org/wikipedia/commons/thumb/e/ec/DeuxPS2.jpg/440px-DeuxPS2.jpg 2x" data-file-width="710" data-file-height="1000" /></a>
<div class="thumbcaption">
<div class="magnify"><a href="//en.wikipedia.org/wiki/File:DeuxPS2.jpg" class="internal" title="Enlarge"><img src="//bits.wikimedia.org/static-1.24wmf7/skins/common/images/magnify-clip.png" width="15" height="11" alt="" /></a></div>
The externally very similar Models 60 and 80 next to each other</div>
</div>
</div>
<div class="thumb tright">
<div class="thumbinner" style="width:222px;"><a href="//en.wikipedia.org/wiki/File:IBM_Model70_80386.JPG" class="image"><img alt="" src="//upload.wikimedia.org/wikipedia/commons/thumb/0/08/IBM_Model70_80386.JPG/220px-IBM_Model70_80386.JPG" width="220" height="165" class="thumbimage" srcset="//upload.wikimedia.org/wikipedia/commons/thumb/0/08/IBM_Model70_80386.JPG/330px-IBM_Model70_80386.JPG 1.5x, //upload.wikimedia.org/wikipedia/commons/thumb/0/08/IBM_Model70_80386.JPG/440px-IBM_Model70_80386.JPG 2x" data-file-width="2048" data-file-height="1536" /></a>
<div class="thumbcaption">
<div class="magnify"><a href="//en.wikipedia.org/wiki/File:IBM_Model70_80386.JPG" class="internal" title="Enlarge"><img src="//bits.wikimedia.org/static-1.24wmf7/skins/common/images/magnify-clip.png" width="15" height="11" alt="" /></a></div>
IBM Model 70 (case open over case closed)</div>
</div>
</div>
<p>The PS/2 <i>Models 90</i> (IBM 8590/9590) and <i>95</i> (IBM 8595/9595/9595A) used Processor Complex <a href="//en.wikipedia.org/wiki/Daughterboard" title="Daughterboard">daughterboards</a> holding the <a href="//en.wikipedia.org/wiki/Central_processing_unit" title="Central processing unit">CPU</a>, memory controller, MCA interface, and other system components. The available Processor Complex options ranged from the 20&#160;MHz <a href="//en.wikipedia.org/wiki/Intel_80486" title="Intel 80486">Intel 486</a> to the 90&#160;MHz <a href="//en.wikipedia.org/wiki/Intel_P5" title="Intel P5" class="mw-redirect">Pentium</a> and were fully interchangeable. The IBM <i>PC Server 500</i>, which has a motherboard identical to the 9595A, also uses Processor Complexes.</p>
<p>Other later Micro Channel PS/2 models included the Model <i>65SX</i> with a 16&#160;MHz 386SX; various <i>Model 53</i> (IBM 9553), <i>56</i> (IBM 8556) and <i>57</i> (IBM 8557) variants with 386SX, 386SLC or 486SLC2 processors; the <i>Models 76</i> and <i>77</i> (IBM 9576/9577) with 486SX or 486DX2 processors respectively; and the 486-based <i>Model 85</i> (IBM 9585).</p>
<p>The IBM <i><a href="//en.wikipedia.org/wiki/PS/2E" title="PS/2E">PS/2E</a></i> (IBM 9533) was the first <a href="//en.wikipedia.org/wiki/Energy_Star" title="Energy Star">Energy Star</a> compliant personal computer. It had a 50&#160;MHz <a href="//en.wikipedia.org/wiki/IBM_486SLC" title="IBM 486SLC" class="mw-redirect">IBM 486SLC</a> processor, an <a href="//en.wikipedia.org/wiki/Industry_Standard_Architecture" title="Industry Standard Architecture">ISA</a> bus, four <a href="//en.wikipedia.org/wiki/PC_card" title="PC card" class="mw-redirect">PC card</a> slots, and an <a href="//en.wikipedia.org/wiki/Advanced_Technology_Attachment" title="Advanced Technology Attachment" class="mw-redirect">IDE</a> hard drive interface. The environmentally friendly PC borrowed many components from the <a href="//en.wikipedia.org/wiki/ThinkPad" title="ThinkPad">ThinkPad</a> line and was composed of recycled plastics, designed to be easily recycled at the end of its life, and used very little power.</p>
<p>IBM also produced several portable and <a href="//en.wikipedia.org/wiki/Laptop" title="Laptop">laptop</a> PS/2s, including the <i>Model L40</i> (ISA-bus 386SX), <i>N33</i> (IBM's first notebook-format computer from year 1991, Model 8533, 386SX), <i>N51</i> (386SX/SLC), <i>P70</i> (386DX) and <i>P75</i> (486DX2).</p>
<p>The IBM PS/2 <i>Server 195</i> and <i>295</i> (IBM 8600) were 486-based dual-bus MCA network <a href="//en.wikipedia.org/wiki/Server_computer" title="Server computer" class="mw-redirect">servers</a> supporting <a href="//en.wikipedia.org/wiki/Asymmetric_multiprocessing" title="Asymmetric multiprocessing">asymmetric multiprocessing</a>, designed by Parallan Computer Inc.</p>
<p>The IBM <i>PC Server 720</i> (IBM 8642) was the largest MCA-based server made by IBM, although it was not, strictly speaking, a PS/2 model. It could be fitted with up to six Intel Pentium processors interconnected by the <a href="//en.wikipedia.org/w/index.php?title=Corollary_C-bus&amp;action=edit&amp;redlink=1" class="new" title="Corollary C-bus (page does not exist)">Corollary C-bus</a> and up to eighteen SCSI hard disks. This model was equipped with seven combination MCA/<a href="//en.wikipedia.org/wiki/Peripheral_Component_Interconnect" title="Peripheral Component Interconnect" class="mw-redirect">PCI</a> slots.</p>
<p>The IBM <i>ThinkPad 700C</i>, aside from being labeled "700C PS/2" on the case, featured MCA and a 486SLC CPU.</p>
<p><br /></p>
<dl>
<dd>
<div class="notice plainlinks"><i>This list is <a href="//en.wikipedia.org/wiki/Wikipedia:WikiProject_Lists#Incomplete_lists" title="Wikipedia:WikiProject Lists">incomplete</a>; you can help by <a class="external text" href="//en.wikipedia.org/w/index.php?title=IBM_Personal_System/2&amp;action=edit">expanding it</a></i>.</div>
</dd>
</dl>
<table class="wikitable sortable collapsible collapsed">
<tr>
<th>Year</th>
<th>IBM Model Number</th>
<th>Name</th>
<th>Processor</th>
<th>Bus</th>
<th>HD Interface</th>
<th>Case</th>
<th>Notes</th>
</tr>
<tr>
<td>1987</td>
<td>8525-001</td>
<td>Model 25</td>
<td>8&#160;MHz <a href="//en.wikipedia.org/wiki/Intel_8086" title="Intel 8086">Intel 8086</a></td>
<td><a href="//en.wikipedia.org/wiki/Industry_Standard_Architecture" title="Industry Standard Architecture">ISA 8 bit</a></td>
<td></td>
<td>Desktop with integrated Monochrome display</td>
<td>Single disk drive</td>
</tr>
<tr>
<td>1987</td>
<td>8525-004</td>
<td>Model 25</td>
<td>8&#160;MHz <a href="//en.wikipedia.org/wiki/Intel_8086" title="Intel 8086">Intel 8086</a></td>
<td><a href="//en.wikipedia.org/wiki/Industry_Standard_Architecture" title="Industry Standard Architecture">ISA 8 bit</a></td>
<td></td>
<td>Desktop with integrated Color display</td>
<td>Single disk drive</td>
</tr>
<tr>
<td>1987</td>
<td>8525-G01</td>
<td>Model 25</td>
<td>8&#160;MHz <a href="//en.wikipedia.org/wiki/Intel_8086" title="Intel 8086">Intel 8086</a></td>
<td><a href="//en.wikipedia.org/wiki/Industry_Standard_Architecture" title="Industry Standard Architecture">ISA 8 bit</a></td>
<td></td>
<td>Desktop with integrated Monochrome display</td>
<td>Single disk drive, enhanced keyboard</td>
</tr>
<tr>
<td>1987</td>
<td>8525-G04</td>
<td>Model 25</td>
<td>8&#160;MHz <a href="//en.wikipedia.org/wiki/Intel_8086" title="Intel 8086">Intel 8086</a></td>
<td><a href="//en.wikipedia.org/wiki/Industry_Standard_Architecture" title="Industry Standard Architecture">ISA 8 bit</a></td>
<td></td>
<td>Desktop with integrated Color display</td>
<td>Single disk drive, enhanced keyboard</td>
</tr>
<tr>
<td></td>
<td>8525-101</td>
<td>Model 25</td>
<td>8&#160;MHz <a href="//en.wikipedia.org/wiki/Intel_8086" title="Intel 8086">Intel 8086</a></td>
<td><a href="//en.wikipedia.org/wiki/Industry_Standard_Architecture" title="Industry Standard Architecture">ISA 8 bit</a></td>
<td></td>
<td>Desktop with integrated Monochrome display</td>
<td>Single disk drive</td>
</tr>
<tr>
<td></td>
<td>8525-104</td>
<td>Model 25</td>
<td>8&#160;MHz <a href="//en.wikipedia.org/wiki/Intel_8086" title="Intel 8086">Intel 8086</a></td>
<td><a href="//en.wikipedia.org/wiki/Industry_Standard_Architecture" title="Industry Standard Architecture">ISA 8 bit</a></td>
<td></td>
<td>Desktop with integrated Color display</td>
<td>Single disk drive</td>
</tr>
<tr>
<td>1988</td>
<td>8525-L01</td>
<td>Model 25</td>
<td>8&#160;MHz <a href="//en.wikipedia.org/wiki/Intel_8086" title="Intel 8086">Intel 8086</a></td>
<td><a href="//en.wikipedia.org/wiki/Industry_Standard_Architecture" title="Industry Standard Architecture">ISA 8 bit</a></td>
<td></td>
<td>Desktop with integrated Monochrome display</td>
<td>Single disk drive, token-ring network adapter</td>
</tr>
<tr>
<td>1988</td>
<td>8525-L04</td>
<td>Model 25</td>
<td>8&#160;MHz <a href="//en.wikipedia.org/wiki/Intel_8086" title="Intel 8086">Intel 8086</a></td>
<td><a href="//en.wikipedia.org/wiki/Industry_Standard_Architecture" title="Industry Standard Architecture">ISA 8 bit</a></td>
<td></td>
<td>Desktop with integrated Color display</td>
<td>Single disk drive, token-ring network adapter</td>
</tr>
<tr>
<td>1987</td>
<td>8525-C02</td>
<td>Model 25 Collegiate</td>
<td>8&#160;MHz <a href="//en.wikipedia.org/wiki/Intel_8086" title="Intel 8086">Intel 8086</a></td>
<td><a href="//en.wikipedia.org/wiki/Industry_Standard_Architecture" title="Industry Standard Architecture">ISA 8 bit</a></td>
<td></td>
<td>Desktop with integrated Monochrome display</td>
<td>Two disk drives</td>
</tr>
<tr>
<td>1987</td>
<td>8525-C05</td>
<td>Model 25 Collegiate</td>
<td>8&#160;MHz <a href="//en.wikipedia.org/wiki/Intel_8086" title="Intel 8086">Intel 8086</a></td>
<td><a href="//en.wikipedia.org/wiki/Industry_Standard_Architecture" title="Industry Standard Architecture">ISA 8 bit</a></td>
<td></td>
<td>Desktop with integrated Color display</td>
<td>Two disk drives</td>
</tr>
<tr>
<td>1987</td>
<td>8525-K02</td>
<td>Model 25 Collegiate</td>
<td>8&#160;MHz <a href="//en.wikipedia.org/wiki/Intel_8086" title="Intel 8086">Intel 8086</a></td>
<td><a href="//en.wikipedia.org/wiki/Industry_Standard_Architecture" title="Industry Standard Architecture">ISA 8 bit</a></td>
<td></td>
<td>Desktop with integrated Monochrome display</td>
<td>Two disk drives, enhanced keyboard</td>
</tr>
<tr>
<td>1987</td>
<td>8525-K05</td>
<td>Model 25 Collegiate</td>
<td>8&#160;MHz <a href="//en.wikipedia.org/wiki/Intel_8086" title="Intel 8086">Intel 8086</a></td>
<td><a href="//en.wikipedia.org/wiki/Industry_Standard_Architecture" title="Industry Standard Architecture">ISA 8 bit</a></td>
<td></td>
<td>Desktop with integrated Color display</td>
<td>Two disk drives, enhanced keyboard</td>
</tr>
<tr>
<td>1990</td>
<td>8525-006</td>
<td>Model 25 286</td>
<td>10&#160;MHz <a href="//en.wikipedia.org/wiki/Intel_80286" title="Intel 80286">Intel 286</a></td>
<td><a href="//en.wikipedia.org/wiki/Industry_Standard_Architecture" title="Industry Standard Architecture">ISA 16 bit</a></td>
<td></td>
<td>Desktop with integrated Color display</td>
<td>Single HD disk drive</td>
</tr>
<tr>
<td>1990</td>
<td>8525-036</td>
<td>Model 25 286</td>
<td>10&#160;MHz <a href="//en.wikipedia.org/wiki/Intel_80286" title="Intel 80286">Intel 286</a></td>
<td><a href="//en.wikipedia.org/wiki/Industry_Standard_Architecture" title="Industry Standard Architecture">ISA 16 bit</a></td>
<td><a href="//en.wikipedia.org/wiki/ST-506" title="ST-506">ST-506</a>, 30MB drive</td>
<td>Desktop with integrated Color display</td>
<td>Single HD disk drive</td>
</tr>
<tr>
<td>1990</td>
<td>8525-G06</td>
<td>Model 25 286</td>
<td>10&#160;MHz <a href="//en.wikipedia.org/wiki/Intel_80286" title="Intel 80286">Intel 286</a></td>
<td><a href="//en.wikipedia.org/wiki/Industry_Standard_Architecture" title="Industry Standard Architecture">ISA 16 bit</a></td>
<td></td>
<td>Desktop with integrated Color display</td>
<td>Single HD disk drive, enhanced keyboard</td>
</tr>
<tr>
<td>1990</td>
<td>8525-G36</td>
<td>Model 25 286</td>
<td>10&#160;MHz <a href="//en.wikipedia.org/wiki/Intel_80286" title="Intel 80286">Intel 286</a></td>
<td><a href="//en.wikipedia.org/wiki/Industry_Standard_Architecture" title="Industry Standard Architecture">ISA 16 bit</a></td>
<td><a href="//en.wikipedia.org/wiki/ST-506" title="ST-506">ST-506</a>, 30MB drive</td>
<td>Desktop with integrated Color display</td>
<td>Single HD disk drive, enhanced keyboard</td>
</tr>
<tr>
<td>1992</td>
<td>8525-K00</td>
<td>Model 25 SX</td>
<td>16&#160;MHz <a href="//en.wikipedia.org/wiki/Intel_80386" title="Intel 80386">Intel 386 SX</a></td>
<td><a href="//en.wikipedia.org/wiki/Industry_Standard_Architecture" title="Industry Standard Architecture">ISA 16 bit</a></td>
<td></td>
<td>Desktop with integrated Color display</td>
<td>Single HD disk drive</td>
</tr>
<tr>
<td>1992</td>
<td>8525-K01</td>
<td>Model 25 SX</td>
<td>16&#160;MHz <a href="//en.wikipedia.org/wiki/Intel_80386" title="Intel 80386">Intel 386 SX</a></td>
<td><a href="//en.wikipedia.org/wiki/Industry_Standard_Architecture" title="Industry Standard Architecture">ISA 16 bit</a></td>
<td></td>
<td>Desktop with integrated Color display</td>
<td>Single HD disk drive, Ethernet network adapter</td>
</tr>
<tr>
<td>1992</td>
<td>8525-L02</td>
<td>Model 25 SX</td>
<td>16&#160;MHz <a href="//en.wikipedia.org/wiki/Intel_80386" title="Intel 80386">Intel 386 SX</a></td>
<td><a href="//en.wikipedia.org/wiki/Industry_Standard_Architecture" title="Industry Standard Architecture">ISA 16 bit</a></td>
<td></td>
<td>Desktop with integrated Color display</td>
<td>Single HD disk drive, Token-ring network adapter</td>
</tr>
<tr>
<td>1989</td>
<td>8530-001</td>
<td>Model 30</td>
<td>8&#160;MHz <a href="//en.wikipedia.org/wiki/Intel_8086" title="Intel 8086">Intel 8086</a></td>
<td><a href="//en.wikipedia.org/wiki/Industry_Standard_Architecture" title="Industry Standard Architecture">ISA 8 bit</a></td>
<td></td>
<td>Desktop</td>
<td>Single disk drive</td>
</tr>
<tr>
<td>1987</td>
<td>8530-002</td>
<td>Model 30</td>
<td>8&#160;MHz <a href="//en.wikipedia.org/wiki/Intel_8086" title="Intel 8086">Intel 8086</a></td>
<td><a href="//en.wikipedia.org/wiki/Industry_Standard_Architecture" title="Industry Standard Architecture">ISA 8 bit</a></td>
<td></td>
<td>Desktop</td>
<td>Two disk drives</td>
</tr>
<tr>
<td>1987</td>
<td>8530-021</td>
<td>Model 30</td>
<td>8&#160;MHz <a href="//en.wikipedia.org/wiki/Intel_8086" title="Intel 8086">Intel 8086</a></td>
<td><a href="//en.wikipedia.org/wiki/Industry_Standard_Architecture" title="Industry Standard Architecture">ISA 8 bit</a></td>
<td>WDI-325Q, 20MB, <a href="//en.wikipedia.org/wiki/Enhanced_Small_Disk_Interface" title="Enhanced Small Disk Interface">ESDI</a></td>
<td>Desktop</td>
<td>Single disk drives</td>
</tr>
<tr>
<td>1988</td>
<td>8530-E01</td>
<td>Model 30 286</td>
<td>10&#160;MHz <a href="//en.wikipedia.org/wiki/Intel_80286" title="Intel 80286">Intel 286</a></td>
<td><a href="//en.wikipedia.org/wiki/Industry_Standard_Architecture" title="Industry Standard Architecture">ISA 16 bit</a></td>
<td></td>
<td>Desktop</td>
<td>Single HD disk drive</td>
</tr>
<tr>
<td>1988</td>
<td>8530-E21</td>
<td>Model 30 286</td>
<td>10&#160;MHz <a href="//en.wikipedia.org/wiki/Intel_80286" title="Intel 80286">Intel 286</a></td>
<td><a href="//en.wikipedia.org/wiki/Industry_Standard_Architecture" title="Industry Standard Architecture">ISA 16 bit</a></td>
<td><a href="//en.wikipedia.org/wiki/Enhanced_Small_Disk_Interface" title="Enhanced Small Disk Interface">ESDI</a>, 20MB</td>
<td>Desktop</td>
<td>Single HD disk drive</td>
</tr>
<tr>
<td>1989</td>
<td>8530-E31</td>
<td>Model 30 286</td>
<td>10&#160;MHz <a href="//en.wikipedia.org/wiki/Intel_80286" title="Intel 80286">Intel 286</a></td>
<td><a href="//en.wikipedia.org/wiki/Industry_Standard_Architecture" title="Industry Standard Architecture">ISA 16 bit</a></td>
<td><a href="//en.wikipedia.org/wiki/Enhanced_Small_Disk_Interface" title="Enhanced Small Disk Interface">ESDI</a>, 30MB</td>
<td>Desktop</td>
<td>Single HD disk drive</td>
</tr>
<tr>
<td>1991</td>
<td>8530-E41</td>
<td>Model 30 286</td>
<td>10&#160;MHz <a href="//en.wikipedia.org/wiki/Intel_80286" title="Intel 80286">Intel 286</a></td>
<td><a href="//en.wikipedia.org/wiki/Industry_Standard_Architecture" title="Industry Standard Architecture">ISA 16 bit</a></td>
<td><a href="//en.wikipedia.org/wiki/Enhanced_Small_Disk_Interface" title="Enhanced Small Disk Interface">ESDI</a>, 45MB</td>
<td>Desktop</td>
<td>Single HD disk drive</td>
</tr>
<tr>
<td>1987</td>
<td>8530-R02</td>
<td>Model 30</td>
<td>8&#160;MHz <a href="//en.wikipedia.org/wiki/Intel_8086" title="Intel 8086">Intel 8086</a></td>
<td><a href="//en.wikipedia.org/wiki/Industry_Standard_Architecture" title="Industry Standard Architecture">ISA 8 bit</a></td>
<td></td>
<td>Desktop, intelligent financial workstation</td>
<td>Two disk drives, 4700 host support</td>
</tr>
<tr>
<td>1987</td>
<td>8530-R21</td>
<td>Model 30</td>
<td>8&#160;MHz <a href="//en.wikipedia.org/wiki/Intel_8086" title="Intel 8086">Intel 8086</a></td>
<td><a href="//en.wikipedia.org/wiki/Industry_Standard_Architecture" title="Industry Standard Architecture">ISA 8 bit</a></td>
<td><a href="//en.wikipedia.org/wiki/ST-506" title="ST-506">ST-506</a>, 20MB</td>
<td>Desktop, intelligent financial workstation</td>
<td>Single disk drives, 4700 host support</td>
</tr>
<tr>
<td>1988</td>
<td>8530-E0R</td>
<td>Model 30 286</td>
<td>10&#160;MHz <a href="//en.wikipedia.org/wiki/Intel_80286" title="Intel 80286">Intel 286</a></td>
<td><a href="//en.wikipedia.org/wiki/Industry_Standard_Architecture" title="Industry Standard Architecture">ISA 16 bit</a></td>
<td></td>
<td>Desktop, intelligent financial workstation</td>
<td>Single HD disk drive, 4700 host support</td>
</tr>
<tr>
<td>1988</td>
<td>8530-E2R</td>
<td>Model 30 286</td>
<td>10&#160;MHz <a href="//en.wikipedia.org/wiki/Intel_80286" title="Intel 80286">Intel 286</a></td>
<td><a href="//en.wikipedia.org/wiki/Industry_Standard_Architecture" title="Industry Standard Architecture">ISA 16 bit</a></td>
<td><a href="//en.wikipedia.org/wiki/Enhanced_Small_Disk_Interface" title="Enhanced Small Disk Interface">ESDI</a>, 20MB</td>
<td>Desktop, intelligent financial workstation</td>
<td>Single HD disk drive, 4700 host support</td>
</tr>
<tr>
<td>1987</td>
<td>8550-021</td>
<td>50</td>
<td><a href="//en.wikipedia.org/wiki/Intel_80286" title="Intel 80286">Intel 80286</a></td>
<td><a href="//en.wikipedia.org/wiki/Micro_Channel_architecture" title="Micro Channel architecture">MCA 16 bit</a></td>
<td></td>
<td>desktop</td>
</tr>
<tr>
<td>1987</td>
<td>8560</td>
<td>60</td>
<td><a href="//en.wikipedia.org/wiki/Intel_80286" title="Intel 80286">Intel 80286</a></td>
<td><a href="//en.wikipedia.org/wiki/Micro_Channel_architecture" title="Micro Channel architecture">MCA 16 bit</a></td>
<td><a href="//en.wikipedia.org/wiki/Modified_Frequency_Modulation" title="Modified Frequency Modulation">MFM</a> or <a href="//en.wikipedia.org/wiki/Enhanced_Small_Disk_Interface" title="Enhanced Small Disk Interface">ESDI</a></td>
<td>tower</td>
</tr>
<tr>
<td>1987</td>
<td>8580</td>
<td>80</td>
<td><a href="//en.wikipedia.org/wiki/Intel_80386" title="Intel 80386">Intel 80386 DX</a></td>
<td><a href="//en.wikipedia.org/wiki/Micro_Channel_architecture" title="Micro Channel architecture">MCA 32 bit</a></td>
<td><a href="//en.wikipedia.org/wiki/Modified_Frequency_Modulation" title="Modified Frequency Modulation">MFM</a> or <a href="//en.wikipedia.org/wiki/Enhanced_Small_Disk_Interface" title="Enhanced Small Disk Interface">ESDI</a></td>
<td>tower</td>
</tr>
<tr>
<td>1988</td>
<td>8550-031, 8550-061</td>
<td>50 Z</td>
<td><a href="//en.wikipedia.org/wiki/Intel_80286" title="Intel 80286">Intel 80286</a></td>
<td><a href="//en.wikipedia.org/wiki/Micro_Channel_architecture" title="Micro Channel architecture">MCA 16 bit</a></td>
<td></td>
<td>desktop</td>
</tr>
<tr>
<td>1988</td>
<td>8570</td>
<td>70</td>
<td><a href="//en.wikipedia.org/wiki/Intel_80386" title="Intel 80386">Intel 80386 DX</a></td>
<td><a href="//en.wikipedia.org/wiki/Micro_Channel_architecture" title="Micro Channel architecture">MCA 32 bit</a></td>
<td></td>
<td>desktop</td>
</tr>
<tr>
<td>1989</td>
<td>8555</td>
<td>55 SX</td>
<td><a href="//en.wikipedia.org/wiki/Intel_80386" title="Intel 80386">Intel 80386 SX</a></td>
<td><a href="//en.wikipedia.org/wiki/Micro_Channel_architecture" title="Micro Channel architecture">MCA 16 bit</a></td>
<td><a href="//en.wikipedia.org/wiki/Enhanced_Small_Disk_Interface" title="Enhanced Small Disk Interface">ESDI</a></td>
<td>desktop</td>
</tr>
<tr>
<td>1989</td>
<td>8570</td>
<td>70</td>
<td><a href="//en.wikipedia.org/wiki/Intel_80386" title="Intel 80386">Intel 80386 DX</a>, <a href="//en.wikipedia.org/wiki/Intel_80486" title="Intel 80486">Intel 80486</a></td>
<td><a href="//en.wikipedia.org/wiki/Micro_Channel_architecture" title="Micro Channel architecture">MCA 32 bit</a></td>
<td></td>
<td>desktop</td>
</tr>
<tr>
<td>1989</td>
<td>8573-031</td>
<td>P70 386</td>
<td><a href="//en.wikipedia.org/wiki/Intel_80386" title="Intel 80386">Intel 80386 DX</a></td>
<td><a href="//en.wikipedia.org/wiki/Micro_Channel_architecture" title="Micro Channel architecture">MCA 32 bit</a></td>
<td></td>
<td>portable</td>
<td>2 MCA slots, one full length, one half length.</td>
</tr>
<tr>
<td>1990</td>
<td>8573</td>
<td>P75 486</td>
<td><a href="//en.wikipedia.org/wiki/Intel_80486" title="Intel 80486">Intel 80486</a></td>
<td><a href="//en.wikipedia.org/wiki/Micro_Channel_architecture" title="Micro Channel architecture">MCA 32 bit</a></td>
<td><a href="//en.wikipedia.org/wiki/SCSI" title="SCSI">SCSI</a></td>
<td>portable</td>
<td>4 MCA slots, two full length, two half length.</td>
</tr>
<tr>
<td>1990</td>
<td>8580</td>
<td>80</td>
<td><a href="//en.wikipedia.org/wiki/Intel_80386" title="Intel 80386">Intel 80386 DX</a></td>
<td><a href="//en.wikipedia.org/wiki/Micro_Channel_architecture" title="Micro Channel architecture">MCA 32 bit</a></td>
<td><a href="//en.wikipedia.org/wiki/SCSI" title="SCSI">SCSI</a></td>
<td>tower</td>
</tr>
<tr>
<td>1990</td>
<td>8556</td>
<td>56 SX</td>
<td><a href="//en.wikipedia.org/wiki/Intel_80386" title="Intel 80386">Intel 80386 SX</a></td>
<td><a href="//en.wikipedia.org/wiki/Micro_Channel_architecture" title="Micro Channel architecture">MCA 16 bit</a></td>
<td></td>
<td>desktop</td>
</tr>
<tr>
<td>1990</td>
<td>8565</td>
<td>65 SX</td>
<td><a href="//en.wikipedia.org/wiki/Intel_80386" title="Intel 80386">Intel 80386 SX</a></td>
<td><a href="//en.wikipedia.org/wiki/Micro_Channel_architecture" title="Micro Channel architecture">MCA 16 bit</a></td>
<td><a href="//en.wikipedia.org/wiki/SCSI" title="SCSI">SCSI</a></td>
<td>tower</td>
</tr>
<tr>
<td>1990</td>
<td>8590</td>
<td>90 XP 486</td>
<td><a href="//en.wikipedia.org/wiki/Intel_80486" title="Intel 80486">Intel 80486</a></td>
<td><a href="//en.wikipedia.org/wiki/Micro_Channel_architecture" title="Micro Channel architecture">MCA 32 bit</a></td>
<td><a href="//en.wikipedia.org/wiki/SCSI" title="SCSI">SCSI</a></td>
<td>desktop</td>
</tr>
<tr>
<td>1990</td>
<td>8595</td>
<td>95 XP 486</td>
<td><a href="//en.wikipedia.org/wiki/Intel_80486" title="Intel 80486">Intel 80486</a></td>
<td><a href="//en.wikipedia.org/wiki/Micro_Channel_architecture" title="Micro Channel architecture">MCA 32 bit</a></td>
<td><a href="//en.wikipedia.org/wiki/SCSI" title="SCSI">SCSI</a></td>
<td>tower</td>
</tr>
<tr>
<td>1991</td>
<td>8540</td>
<td>40 SX</td>
<td><a href="//en.wikipedia.org/wiki/Intel_80386" title="Intel 80386">Intel 80386 SX</a></td>
<td><a href="//en.wikipedia.org/wiki/Industry_Standard_Architecture" title="Industry Standard Architecture">ISA 16 bit</a></td>
<td></td>
<td>desktop</td>
</tr>
<tr>
<td>1991</td>
<td>8557</td>
<td>57 SX</td>
<td><a href="//en.wikipedia.org/wiki/Intel_80386" title="Intel 80386">Intel 80386 SX</a></td>
<td><a href="//en.wikipedia.org/wiki/Micro_Channel_architecture" title="Micro Channel architecture">MCA 16 bit</a></td>
<td><a href="//en.wikipedia.org/wiki/SCSI" title="SCSI">SCSI</a></td>
<td>desktop</td>
</tr>
<tr>
<td>1991</td>
<td>8570</td>
<td>70</td>
<td><a href="//en.wikipedia.org/wiki/Intel_80386" title="Intel 80386">Intel 80386 DX</a>, <a href="//en.wikipedia.org/wiki/Intel_80486" title="Intel 80486">Intel 80486</a></td>
<td><a href="//en.wikipedia.org/wiki/Micro_Channel_architecture" title="Micro Channel architecture">MCA 32 bit</a></td>
<td></td>
<td>desktop</td>
</tr>
</table>
<h2><span class="mw-headline" id="Marketing">Marketing</span></h2>
<p>The PS/2's controversial hardware design was tied to a <a href="//en.wikipedia.org/wiki/Marketing" title="Marketing">marketing</a> strategy that was similarly unsuccessful. During the 1980s, IBM's advertising of the <a href="//en.wikipedia.org/wiki/IBM_Personal_Computer" title="IBM Personal Computer">original PC</a> and its other product lines had frequently used the likeness of <a href="//en.wikipedia.org/wiki/Charlie_Chaplin" title="Charlie Chaplin">Charlie Chaplin</a>. For the PS/2, however, IBM augmented this character with a notorious jingle that seemed more suitable for a low-end consumer product than a business-class computing platform:</p>
<dl>
<dd>“How ya' gonna' do it?<br />
PS/2 It!<br />
It's as easy as I.B.M.”</dd>
</dl>
<dl>
<dd>“How ya' gonna' do it?<br />
PS/2 It!<br />
The solution is I.B.M.”</dd>
</dl>
<p>Another campaign featured actors from the television show <a href="//en.wikipedia.org/wiki/M*A*S*H_(TV_series)" title="M*A*S*H (TV series)">M*A*S*H</a> playing the staff of a contemporary (i.e. late-1980s) business in roles reminiscent of their characters' roles from the series. Harry Morgan, Gary Burghoff, Jamie Farr, and Loretta Swit were in from the beginning, whereas Alan Alda joined the campaign later.</p>
<p>The profound lack of success of these advertising campaigns led, in part, to IBM's termination of its relationships with its global <a href="//en.wikipedia.org/wiki/Advertising_agency" title="Advertising agency">advertising agencies</a>; these accounts were reported by <a href="//en.wikipedia.org/wiki/Wired_(magazine)" title="Wired (magazine)"><i>Wired</i></a> magazine to have been worth over $500 million a year, and the largest such account review in the history of business.<sup id="cite_ref-Advert_5-0" class="reference"><a href="#cite_note-Advert-5"><span>[</span>5<span>]</span></a></sup></p>
<p>Overall, the PS/2 line was largely unsuccessful with the consumer market, even though the PC based Models 30 and 25 were an attempt to address it. With what was widely seen as a technically competent but <a href="//en.wikipedia.org/wiki/Cynicism_(contemporary)" title="Cynicism (contemporary)">cynical</a> attempt to gain undisputed control of the market, IBM unleashed an industry and consumer backlash. The firm suffered massive financial losses for the remainder of the decade, forfeited its previously unquestioned position as the industry leader, and eventually lost its status as the largest single manufacturer of personal computers, first to <a href="//en.wikipedia.org/wiki/Compaq" title="Compaq">Compaq</a> and then to <a href="//en.wikipedia.org/wiki/Dell,_Inc." title="Dell, Inc." class="mw-redirect">Dell</a>. After the failure of the PS/2 line to establish a new standard, IBM was forced to revert to building ISA PCs—following the industry it had once led—with the <a href="//en.wikipedia.org/wiki/IBM_PS/1" title="IBM PS/1">PS/1</a> line and later the <a href="//en.wikipedia.org/wiki/IBM_Aptiva" title="IBM Aptiva">Aptiva</a> and <a href="//en.wikipedia.org/wiki/IBM_PS/ValuePoint" title="IBM PS/ValuePoint">PS/ValuePoint</a> lines. Eventually, IBM sold its entire PC business to <a href="//en.wikipedia.org/wiki/Lenovo" title="Lenovo">Lenovo</a>.</p>
<p>Still, the PS/2 platform experienced success in the business sector where the reliability, ease of maintenance and strong support from IBM offset the rather daunting cost of the machines. Also, many people still lived with the motto "Nobody ever got fired for buying an IBM." The model 55SX and later 56SX were the leading sellers for almost their entire lifetimes. Many models of PS/2 systems saw a production life span that took them well into the late 1990s.</p>
<h2><span class="mw-headline" id="See_also">See also</span></h2>
<ul>
<li><a href="//en.wikipedia.org/wiki/IBM_Personal_Computer/AT" title="IBM Personal Computer/AT">IBM Personal Computer/AT</a></li>
<li><a href="//en.wikipedia.org/wiki/IBM_PS/1" title="IBM PS/1">IBM PS/1</a></li>
<li><a href="//en.wikipedia.org/wiki/IBM_PC_Series" title="IBM PC Series">IBM PC Series</a></li>
</ul>
<h2><span class="mw-headline" id="References">References</span></h2>
<div class="reflist" style="list-style-type: decimal;">
<ol class="references">
<li id="cite_note-byte198706-1"><span class="mw-cite-backlink">^ <a href="#cite_ref-byte198706_1-0"><sup><i><b>a</b></i></sup></a> <a href="#cite_ref-byte198706_1-1"><sup><i><b>b</b></i></sup></a> <a href="#cite_ref-byte198706_1-2"><sup><i><b>c</b></i></sup></a> <a href="#cite_ref-byte198706_1-3"><sup><i><b>d</b></i></sup></a> <a href="#cite_ref-byte198706_1-4"><sup><i><b>e</b></i></sup></a> <a href="#cite_ref-byte198706_1-5"><sup><i><b>f</b></i></sup></a> <a href="#cite_ref-byte198706_1-6"><sup><i><b>g</b></i></sup></a></span> <span class="reference-text"><span class="citation news">BYTE editorial staff (June 1987). <a rel="nofollow" class="external text" href="https://archive.org/stream/byte-magazine-1987-06/1987_06_BYTE_12-06_CAD_Mice_12-MHz_ATs_IBM_PS2_Family#page/n131/mode/2up">"The IBM PS/2 Computers"</a>. <i>BYTE</i>. p.&#160;100<span class="reference-accessdate">. Retrieved 5 November 2013</span>.</span><span title="ctx_ver=Z39.88-2004&amp;rfr_id=info%3Asid%2Fen.wikipedia.org%3AIBM+Personal+System%2F2&amp;rft.atitle=The+IBM+PS%2F2+Computers&amp;rft.au=BYTE+editorial+staff&amp;rft.aulast=BYTE+editorial+staff&amp;rft.date=June+1987&amp;rft.genre=article&amp;rft_id=https%3A%2F%2Farchive.org%2Fstream%2Fbyte-magazine-1987-06%2F1987_06_BYTE_12-06_CAD_Mice_12-MHz_ATs_IBM_PS2_Family%23page%2Fn131%2Fmode%2F2up&amp;rft.jtitle=BYTE&amp;rft.pages=100&amp;rft_val_fmt=info%3Aofi%2Ffmt%3Akev%3Amtx%3Ajournal" class="Z3988"><span style="display:none;">&#160;</span></span></span></li>
<li id="cite_note-Held2000-2"><span class="mw-cite-backlink"><b><a href="#cite_ref-Held2000_2-0">^</a></b></span> <span class="reference-text"><span class="citation book">Gilbert Held (2000). <a rel="nofollow" class="external text" href="http://books.google.com/books?id=Y9q-i_HGtAAC&amp;pg=PA199"><i>Server Management</i></a>. CRC Press. p.&#160;199. <a href="//en.wikipedia.org/wiki/International_Standard_Book_Number" title="International Standard Book Number">ISBN</a>&#160;<a href="//en.wikipedia.org/wiki/Special:BookSources/978-1-4200-3106-5" title="Special:BookSources/978-1-4200-3106-5">978-1-4200-3106-5</a>.</span><span title="ctx_ver=Z39.88-2004&amp;rfr_id=info%3Asid%2Fen.wikipedia.org%3AIBM+Personal+System%2F2&amp;rft.au=Gilbert+Held&amp;rft.aulast=Gilbert+Held&amp;rft.btitle=Server+Management&amp;rft.date=2000&amp;rft.genre=book&amp;rft_id=http%3A%2F%2Fbooks.google.com%2Fbooks%3Fid%3DY9q-i_HGtAAC%26pg%3DPA199&amp;rft.isbn=978-1-4200-3106-5&amp;rft.pages=199&amp;rft.pub=CRC+Press&amp;rft_val_fmt=info%3Aofi%2Ffmt%3Akev%3Amtx%3Abook" class="Z3988"><span style="display:none;">&#160;</span></span></span></li>
<li id="cite_note-3"><span class="mw-cite-backlink"><b><a href="#cite_ref-3">^</a></b></span> <span class="reference-text"><span class="citation web">Jim Porter (1998-12-14). <a rel="nofollow" class="external text" href="http://web.archive.org/web/20120328044723/http://www.disktrend.com/pdf/portrpkg.pdf">"100th Anniversary Conference: Magnetic Recording and Information Storage"</a>. disktrend.com. Archived from <a rel="nofollow" class="external text" href="http://www.disktrend.com/pdf/portrpkg.pdf">the original</a> on 2012-03-28<span class="reference-accessdate">. Retrieved 2014-03-24</span>.</span><span title="ctx_ver=Z39.88-2004&amp;rfr_id=info%3Asid%2Fen.wikipedia.org%3AIBM+Personal+System%2F2&amp;rft.au=Jim+Porter&amp;rft.aulast=Jim+Porter&amp;rft.btitle=100th+Anniversary+Conference%3A+Magnetic+Recording+and+Information+Storage&amp;rft.date=1998-12-14&amp;rft.genre=book&amp;rft_id=http%3A%2F%2Fwww.disktrend.com%2Fpdf%2Fportrpkg.pdf&amp;rft.pub=disktrend.com&amp;rft_val_fmt=info%3Aofi%2Ffmt%3Akev%3Amtx%3Abook" class="Z3988"><span style="display:none;">&#160;</span></span></span></li>
<li id="cite_note-4"><span class="mw-cite-backlink"><b><a href="#cite_ref-4">^</a></b></span> <span class="reference-text"><a rel="nofollow" class="external text" href="http://ohlandl.ipv7.net/floppy/floppy.html#Format_720K_On_144MB">Formatting 720K Disks on a 1.44MB Floppy</a></span></li>
<li id="cite_note-Advert-5"><span class="mw-cite-backlink"><b><a href="#cite_ref-Advert_5-0">^</a></b></span> <span class="reference-text"><i>Wired</i>, Issue 3.08, August 1995</span></li>
</ol>
</div>
<h2><span class="mw-headline" id="Further_reading">Further reading</span></h2>
<ul>
<li>Burton, Greg. <i>IBM PC and PS/2 pocket reference</i>. NDD (the old dealer channel), 1991.</li>
<li>Byers, T.J. <i>IBM PS/2: A Reference Guide</i>. Intertext Publications, 1989. <a href="//en.wikipedia.org/wiki/Special:BookSources/0070095256" class="internal mw-magiclink-isbn">ISBN 0-07-009525-6</a>.</li>
<li>Dalton, Richard and Mueller, Scott. <i>IBM PS/2 Handbook</i> . Que Publications, 1989. <a href="//en.wikipedia.org/wiki/Special:BookSources/0880223340" class="internal mw-magiclink-isbn">ISBN 0-88022-334-0</a>.</li>
<li>Held, Gilbert. <i>IBM PS/2: User's Reference Manual</i>. John Wiley &amp; Sons Inc., 1989. <a href="//en.wikipedia.org/wiki/Special:BookSources/0471621501" class="internal mw-magiclink-isbn">ISBN 0-471-62150-1</a>.</li>
<li>Hoskins, Jim. <i>IBM PS/2</i>. John Wiley &amp; Sons Inc., fifth revised edition, 1992. <a href="//en.wikipedia.org/wiki/Special:BookSources/0471551953" class="internal mw-magiclink-isbn">ISBN 0-471-55195-3</a>.</li>
<li>Leghart, Paul M. <i>The IBM PS/2 in-depth report</i>. Pachogue, NY: Computer Technology Research Corporation, 1988.</li>
<li>Newcom, Kerry. <i>A Closer Look at IBM PS/2 Microchannel Architecture</i>. New York: McGraw-Hill, 1988.</li>
<li>Norton, Peter. <i>Inside the IBM PC and PS/2</i>. Brady Publishing, fourth edition 1991. <a href="//en.wikipedia.org/wiki/Special:BookSources/0134656342" class="internal mw-magiclink-isbn">ISBN 0-13-465634-2</a>.</li>
<li><i>Outside the IBM PC and PS/2: Access to New Technology</i>. Brady Publishing, 1992. <a href="//en.wikipedia.org/wiki/Special:BookSources/0136435866" class="internal mw-magiclink-isbn">ISBN 0-13-643586-6</a>.</li>
<li>Shanley, Tom. <i>IBM PS/2 from the Inside Out</i>. Addison-Wesley, 1991. <a href="//en.wikipedia.org/wiki/Special:BookSources/0201570564" class="internal mw-magiclink-isbn">ISBN 0-201-57056-4</a>.</li>
</ul>
<h2><span class="mw-headline" id="External_links">External links</span></h2>
<table class="metadata mbox-small plainlinks" style="border:1px solid #aaa;background-color:#f9f9f9;">
<tr>
<td class="mbox-image"><img alt="" src="//upload.wikimedia.org/wikipedia/en/thumb/4/4a/Commons-logo.svg/30px-Commons-logo.svg.png" width="30" height="40" srcset="//upload.wikimedia.org/wikipedia/en/thumb/4/4a/Commons-logo.svg/45px-Commons-logo.svg.png 1.5x, //upload.wikimedia.org/wikipedia/en/thumb/4/4a/Commons-logo.svg/59px-Commons-logo.svg.png 2x" data-file-width="1024" data-file-height="1376" /></td>
<td class="mbox-text plainlist">Wikimedia Commons has media related to <i><b><a href="//commons.wikimedia.org/wiki/Category:IBM_Personal_System/2" class="extiw" title="commons:Category:IBM Personal System/2">IBM Personal System/2</a></b></i>.</td>
</tr>
</table>
<ul>
<li><a rel="nofollow" class="external text" href="http://retropc.org/index.html?action=w_podrazdela&amp;id_podrazdel=145&amp;id_soderjanie=91">IBM Type 8530</a></li>
<li><a rel="nofollow" class="external text" href="http://www.lenovo.com/psref/pdf/ps2book.pdf">IBM PS/2 Personal Systems Reference Guide</a> 1992 - 1995</li>
<li><a rel="nofollow" class="external text" href="http://www.computercraft.com/docs/ps2sect.shtml">Computercraft - The PS/2 Resource Center</a></li>
<li><a rel="nofollow" class="external text" href="http://ohlandl.kev009.com/">Model 9595 Resource, covers all PS/2 models and adapters</a></li>
<li><a rel="nofollow" class="external text" href="http://pinouts.ws/ps-2-keyboard-pinout.html">PS/2 keyboard pinout</a></li>
<li><a rel="nofollow" class="external text" href="http://www.computer-engineering.org/">PS/2 Mouse/Keyboard Interfacing</a></li>
<li><a rel="nofollow" class="external text" href="http://www.archive.org/details/IBMPerso1987">Computer Chronicles episode on the PS/2</a></li>
<li><a rel="nofollow" class="external text" href="http://hpholm.dk/L40SX.html">IBM PS/2 L40 SX (8543)</a></li>
</ul>


<!-- 
NewPP limit report
Parsed by mw1164
CPU time usage: 0.836 seconds
Real time usage: 0.956 seconds
Preprocessor visited node count: 1600/1000000
Preprocessor generated node count: 8748/1500000
Post‐expand include size: 22690/2048000 bytes
Template argument size: 2943/2048000 bytes
Highest expansion depth: 16/40
Expensive parser function count: 7/500
Lua time usage: 0.083/10.000 seconds
Lua memory usage: 2.42 MB/50 MB
-->
